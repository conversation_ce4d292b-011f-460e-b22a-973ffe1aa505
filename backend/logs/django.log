INFO 2025-05-20 10:35:22,796 autoreload 91915 8462421120 Watching for file changes with StatReloader
WARNING 2025-05-20 11:08:26,645 log 91915 6141374464 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-20 11:08:26,646 basehttp 91915 6141374464 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 404 108
WARNING 2025-05-20 11:08:26,675 log 91915 6141374464 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-20 11:08:26,675 basehttp 91915 6141374464 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 404 108
WARNING 2025-05-20 11:08:58,978 log 91915 6141374464 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-20 11:08:58,979 basehttp 91915 6141374464 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 404 108
WARNING 2025-05-20 11:08:58,999 log 91915 6141374464 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-20 11:08:59,000 basehttp 91915 6141374464 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 404 108
INFO 2025-05-20 11:09:00,702 basehttp 91915 6141374464 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-20 11:09:00,733 basehttp 91915 6175027200 "GET /api/auth/profile/ HTTP/1.1" 200 194
INFO 2025-05-20 11:09:00,737 basehttp 91915 6141374464 "PUT /api/auth/profile/ HTTP/1.1" 200 219
WARNING 2025-05-20 11:09:00,739 log 91915 6191853568 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-20 11:09:00,739 basehttp 91915 6191853568 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 404 108
INFO 2025-05-20 11:09:02,722 basehttp 91915 6141374464 "PUT /api/auth/profile/ HTTP/1.1" 200 219
INFO 2025-05-20 11:09:02,728 basehttp 91915 6175027200 "PUT /api/auth/profile/ HTTP/1.1" 200 219
WARNING 2025-05-20 11:09:02,730 log 91915 6191853568 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-20 11:09:02,731 basehttp 91915 6191853568 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 404 108
INFO 2025-05-20 11:09:04,403 basehttp 91915 6141374464 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 3214
INFO 2025-05-20 11:09:04,423 basehttp 91915 6141374464 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 3214
INFO 2025-05-20 14:56:44,658 basehttp 91915 6141374464 "GET /api/crypto/get_report/BTCUSDT/?language=en-US HTTP/1.1" 200 2618
INFO 2025-05-20 14:57:33,138 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2922
INFO 2025-05-20 14:57:33,164 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2922
INFO 2025-05-20 14:58:02,990 basehttp 91915 6175600640 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-20 14:58:03,038 basehttp 91915 6209253376 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2922
INFO 2025-05-20 14:58:03,063 basehttp 91915 6192427008 "GET /api/auth/profile/ HTTP/1.1" 200 194
INFO 2025-05-20 14:58:03,066 basehttp 91915 6175600640 "PUT /api/auth/profile/ HTTP/1.1" 200 219
INFO 2025-05-20 14:58:05,940 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2922
INFO 2025-05-20 14:58:05,959 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2922
INFO 2025-05-20 14:58:09,668 basehttp 91915 6192427008 "GET /api/auth/profile/ HTTP/1.1" 200 194
INFO 2025-05-20 14:58:09,669 basehttp 91915 6175600640 "PUT /api/auth/profile/ HTTP/1.1" 200 219
INFO 2025-05-20 14:58:09,681 basehttp 91915 6209253376 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2922
INFO 2025-05-20 14:58:09,696 basehttp 91915 6192427008 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2922
INFO 2025-05-20 14:58:11,526 basehttp 91915 6175600640 "PUT /api/auth/profile/ HTTP/1.1" 200 219
INFO 2025-05-20 14:58:11,530 basehttp 91915 6209253376 "PUT /api/auth/profile/ HTTP/1.1" 200 219
INFO 2025-05-20 14:58:11,536 basehttp 91915 6192427008 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2922
INFO 2025-05-20 14:58:11,554 basehttp 91915 6192427008 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2922
WARNING 2025-05-20 14:58:13,322 log 91915 6192427008 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-20 14:58:13,322 basehttp 91915 6192427008 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 404 108
WARNING 2025-05-20 14:58:13,342 log 91915 6192427008 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-20 14:58:13,342 basehttp 91915 6192427008 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 404 108
INFO 2025-05-20 14:58:29,525 basehttp 91915 6175600640 "GET /api/crypto/get_report/BTCUSDT/?language=ja-JP&_t=1747753096208 HTTP/1.1" 200 3034
INFO 2025-05-20 14:58:30,421 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3334
INFO 2025-05-20 14:58:31,482 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3334
INFO 2025-05-20 15:00:58,705 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3334
INFO 2025-05-20 15:00:58,731 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3334
INFO 2025-05-20 15:01:09,639 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3334
INFO 2025-05-20 15:01:09,678 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3334
INFO 2025-05-20 15:01:40,493 basehttp 91915 6192427008 "GET /api/auth/profile/ HTTP/1.1" 200 194
INFO 2025-05-20 15:01:40,502 basehttp 91915 6209253376 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3334
INFO 2025-05-20 15:01:40,517 basehttp 91915 6175600640 "PUT /api/auth/profile/ HTTP/1.1" 200 219
INFO 2025-05-20 15:01:43,289 basehttp 91915 6175600640 "PUT /api/auth/profile/ HTTP/1.1" 200 219
INFO 2025-05-20 15:01:43,298 basehttp 91915 6209253376 "PUT /api/auth/profile/ HTTP/1.1" 200 219
INFO 2025-05-20 15:01:43,307 basehttp 91915 6192427008 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3334
WARNING 2025-05-20 15:01:45,360 log 91915 6175600640 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-20 15:01:45,360 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 404 108
WARNING 2025-05-20 15:01:45,378 log 91915 6175600640 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-20 15:01:45,378 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 404 108
INFO 2025-05-20 15:01:50,356 basehttp 91915 6192427008 "GET /api/auth/profile/ HTTP/1.1" 200 194
INFO 2025-05-20 15:01:50,363 basehttp 91915 6175600640 "PUT /api/auth/profile/ HTTP/1.1" 200 219
WARNING 2025-05-20 15:01:50,364 log 91915 6209253376 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-20 15:01:50,364 basehttp 91915 6209253376 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 404 108
WARNING 2025-05-20 15:01:50,374 log 91915 6192427008 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-20 15:01:50,374 basehttp 91915 6192427008 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 404 108
INFO 2025-05-20 15:01:52,703 basehttp 91915 6175600640 "PUT /api/auth/profile/ HTTP/1.1" 200 219
INFO 2025-05-20 15:01:52,705 basehttp 91915 6192427008 "PUT /api/auth/profile/ HTTP/1.1" 200 219
WARNING 2025-05-20 15:01:52,708 log 91915 6209253376 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-20 15:01:52,708 basehttp 91915 6209253376 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 404 108
WARNING 2025-05-20 15:01:52,718 log 91915 6192427008 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-20 15:01:52,718 basehttp 91915 6192427008 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 404 108
WARNING 2025-05-20 15:01:54,586 log 91915 6175600640 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-20 15:01:54,586 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 404 108
WARNING 2025-05-20 15:01:54,604 log 91915 6175600640 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-20 15:01:54,604 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 404 108
INFO 2025-05-20 15:02:09,410 basehttp 91915 6175600640 "GET /api/crypto/get_report/BTCUSDT/?language=ko-KR&_t=1747753315504 HTTP/1.1" 200 2753
INFO 2025-05-20 15:02:10,300 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3053
INFO 2025-05-20 15:02:11,326 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3053
INFO 2025-05-20 15:04:20,652 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3053
INFO 2025-05-20 15:04:20,680 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3053
INFO 2025-05-20 15:04:29,196 basehttp 91915 6192427008 "GET /api/auth/profile/ HTTP/1.1" 200 194
INFO 2025-05-20 15:04:29,199 basehttp 91915 6175600640 "PUT /api/auth/profile/ HTTP/1.1" 200 219
INFO 2025-05-20 15:04:29,208 basehttp 91915 6209253376 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3053
INFO 2025-05-20 15:04:31,240 basehttp 91915 6175600640 "PUT /api/auth/profile/ HTTP/1.1" 200 219
INFO 2025-05-20 15:04:31,244 basehttp 91915 6209253376 "PUT /api/auth/profile/ HTTP/1.1" 200 219
INFO 2025-05-20 15:04:31,253 basehttp 91915 6192427008 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3053
INFO 2025-05-20 15:04:32,851 basehttp 91915 6192427008 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3334
INFO 2025-05-20 15:04:32,876 basehttp 91915 6192427008 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3334
INFO 2025-05-20 15:04:33,512 basehttp 91915 6175600640 "PUT /api/auth/profile/ HTTP/1.1" 200 219
INFO 2025-05-20 15:04:33,516 basehttp 91915 6192427008 "GET /api/auth/profile/ HTTP/1.1" 200 194
INFO 2025-05-20 15:04:33,526 basehttp 91915 6209253376 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3334
INFO 2025-05-20 15:04:33,542 basehttp 91915 6192427008 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3334
INFO 2025-05-20 15:04:35,112 basehttp 91915 6175600640 "PUT /api/auth/profile/ HTTP/1.1" 200 219
INFO 2025-05-20 15:04:35,113 basehttp 91915 6192427008 "PUT /api/auth/profile/ HTTP/1.1" 200 219
INFO 2025-05-20 15:04:35,121 basehttp 91915 6209253376 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3334
INFO 2025-05-20 15:04:35,139 basehttp 91915 6192427008 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3334
WARNING 2025-05-20 15:04:36,723 log 91915 6192427008 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-20 15:04:36,723 basehttp 91915 6192427008 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 404 108
WARNING 2025-05-20 15:04:36,749 log 91915 6192427008 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-20 15:04:36,749 basehttp 91915 6192427008 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 404 108
INFO 2025-05-20 15:04:50,477 basehttp 91915 6192427008 "GET /api/crypto/get_report/BTCUSDT/?language=zh-CN&_t=1747753477319 HTTP/1.1" 200 2173
INFO 2025-05-20 15:04:51,432 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2473
INFO 2025-05-20 15:04:52,463 basehttp 91915 6175600640 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2473
INFO 2025-05-21 12:49:04,838 autoreload 91915 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/models.py changed, reloading.
INFO 2025-05-21 12:49:05,798 autoreload 61994 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 12:49:26,683 autoreload 61994 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/models.py changed, reloading.
INFO 2025-05-21 12:49:27,118 autoreload 62051 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 12:49:56,716 autoreload 62051 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin.py changed, reloading.
INFO 2025-05-21 12:49:57,146 autoreload 62110 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 12:50:29,684 autoreload 62110 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/serializers.py changed, reloading.
INFO 2025-05-21 12:50:30,754 autoreload 62396 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 12:50:46,365 autoreload 62396 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/serializers.py changed, reloading.
INFO 2025-05-21 12:50:46,968 autoreload 62457 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 12:51:04,100 autoreload 62457 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/serializers.py changed, reloading.
INFO 2025-05-21 12:51:04,569 autoreload 62497 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 12:51:26,889 autoreload 62497 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/views.py changed, reloading.
INFO 2025-05-21 12:51:27,693 autoreload 62533 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 12:52:00,151 autoreload 62533 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/views.py changed, reloading.
INFO 2025-05-21 12:52:00,679 autoreload 62599 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 12:52:21,351 autoreload 62599 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/views.py changed, reloading.
INFO 2025-05-21 12:52:21,966 autoreload 62635 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 12:52:43,389 autoreload 62635 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/urls.py changed, reloading.
INFO 2025-05-21 12:52:43,835 autoreload 62688 8462421120 Watching for file changes with StatReloader
WARNING 2025-05-21 12:54:09,861 log 62688 6207696896 Unauthorized: /api/auth/generate-invitation-code/
WARNING 2025-05-21 12:54:09,862 basehttp 62688 6207696896 "POST /api/auth/generate-invitation-code/ HTTP/1.1" 401 34
INFO 2025-05-21 12:54:15,533 basehttp 62688 6207696896 "POST /api/auth/login/ HTTP/1.1" 200 294
INFO 2025-05-21 12:54:22,709 basehttp 62688 6207696896 "POST /api/auth/generate-invitation-code/ HTTP/1.1" 201 110
INFO 2025-05-21 12:55:16,307 basehttp 62688 6224523264 "GET /admin/CryptoAnalyst/invitationcode/ HTTP/1.1" 302 0
INFO 2025-05-21 12:55:16,455 basehttp 62688 6224523264 "GET /admin/login/?next=/admin/CryptoAnalyst/invitationcode/ HTTP/1.1" 200 4246
INFO 2025-05-21 12:55:23,038 basehttp 62688 6224523264 "POST /admin/login/?next=/admin/CryptoAnalyst/invitationcode/ HTTP/1.1" 302 0
WARNING 2025-05-21 12:55:23,202 log 62688 6224523264 Not Found: /admin/CryptoAnalyst/invitationcode/
WARNING 2025-05-21 12:55:23,202 basehttp 62688 6224523264 "GET /admin/CryptoAnalyst/invitationcode/ HTTP/1.1" 404 5996
INFO 2025-05-21 12:55:29,167 basehttp 62688 6224523264 "GET /admin/ HTTP/1.1" 200 10438
INFO 2025-05-21 12:55:29,217 basehttp 62688 6224523264 "GET /static/admin/css/base.css HTTP/1.1" 304 0
INFO 2025-05-21 12:55:29,243 basehttp 62688 6224523264 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
INFO 2025-05-21 12:55:29,244 basehttp 62688 6258176000 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
INFO 2025-05-21 12:55:29,248 basehttp 62688 6275002368 "GET /static/admin/css/dashboard.css HTTP/1.1" 304 0
INFO 2025-05-21 12:55:29,251 basehttp 62688 6241349632 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
INFO 2025-05-21 12:55:29,271 basehttp 62688 6275002368 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
INFO 2025-05-21 12:55:29,273 basehttp 62688 6241349632 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
INFO 2025-05-21 12:55:29,278 basehttp 62688 6275002368 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 304 0
INFO 2025-05-21 12:55:29,279 basehttp 62688 6241349632 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 304 0
INFO 2025-05-21 12:55:33,784 basehttp 62688 6224523264 "GET /admin/user/invitationcode/ HTTP/1.1" 200 15308
INFO 2025-05-21 12:55:33,828 basehttp 62688 6241349632 "GET /static/admin/css/changelists.css HTTP/1.1" 304 0
INFO 2025-05-21 12:55:33,829 basehttp 62688 6275002368 "GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
INFO 2025-05-21 12:55:33,832 basehttp 62688 6258176000 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
INFO 2025-05-21 12:55:33,837 basehttp 62688 6291828736 "GET /static/admin/js/core.js HTTP/1.1" 304 0
INFO 2025-05-21 12:55:33,837 basehttp 62688 6241349632 "GET /static/admin/js/actions.js HTTP/1.1" 304 0
INFO 2025-05-21 12:55:33,839 basehttp 62688 13035925504 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
INFO 2025-05-21 12:55:33,841 basehttp 62688 6275002368 "GET /static/admin/js/urlify.js HTTP/1.1" 304 0
INFO 2025-05-21 12:55:33,844 basehttp 62688 6258176000 "GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
INFO 2025-05-21 12:55:33,844 basehttp 62688 13035925504 "GET /static/admin/img/search.svg HTTP/1.1" 304 0
INFO 2025-05-21 12:55:33,846 basehttp 62688 6275002368 "GET /static/admin/img/icon-no.svg HTTP/1.1" 304 0
INFO 2025-05-21 12:55:33,852 basehttp 62688 6291828736 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
INFO 2025-05-21 12:55:33,875 basehttp 62688 6224523264 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 12:55:33,907 basehttp 62688 6224523264 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 304 0
INFO 2025-05-21 12:55:33,908 basehttp 62688 6291828736 "GET /static/admin/js/filters.js HTTP/1.1" 304 0
INFO 2025-05-21 12:55:33,917 basehttp 62688 6291828736 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 304 0
INFO 2025-05-21 12:55:39,062 basehttp 62688 6224523264 "GET /admin/user/invitationcode/add/ HTTP/1.1" 200 17171
INFO 2025-05-21 12:55:39,099 basehttp 62688 6224523264 "GET /static/admin/css/forms.css HTTP/1.1" 200 9090
INFO 2025-05-21 12:55:39,101 basehttp 62688 6258176000 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
INFO 2025-05-21 12:55:39,102 basehttp 62688 6275002368 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
INFO 2025-05-21 12:55:39,106 basehttp 62688 6241349632 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 12:55:39,138 basehttp 62688 6275002368 "GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 200 392
INFO 2025-05-21 12:55:39,138 basehttp 62688 6241349632 "GET /static/admin/css/widgets.css HTTP/1.1" 200 11800
INFO 2025-05-21 12:55:39,141 basehttp 62688 6275002368 "GET /static/admin/js/change_form.js HTTP/1.1" 200 606
INFO 2025-05-21 12:55:46,526 basehttp 62688 6224523264 "GET /admin/user/invitationcode/ HTTP/1.1" 200 15308
INFO 2025-05-21 12:55:46,549 basehttp 62688 6224523264 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 12:55:47,759 basehttp 62688 6224523264 "GET /admin/user/invitationrecord/ HTTP/1.1" 200 11840
INFO 2025-05-21 12:55:47,801 basehttp 62688 6224523264 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 12:55:49,952 basehttp 62688 6224523264 "GET /admin/user/invitationcode/ HTTP/1.1" 200 15308
INFO 2025-05-21 12:55:49,995 basehttp 62688 6224523264 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 12:56:02,709 basehttp 62688 6224523264 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 12:56:02,796 basehttp 62688 6241349632 "GET /api/auth/profile/ HTTP/1.1" 200 234
INFO 2025-05-21 12:56:02,816 basehttp 62688 6224523264 "PUT /api/auth/profile/ HTTP/1.1" 200 259
INFO 2025-05-21 12:56:02,824 basehttp 62688 6258176000 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 12:56:02,856 basehttp 62688 6241349632 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 12:56:02,915 basehttp 62688 6241349632 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 12:56:21,967 basehttp 62688 6224523264 "POST /api/auth/send-code/ HTTP/1.1" 200 51
INFO 2025-05-21 12:56:55,989 basehttp 62688 6224523264 "POST /api/auth/register/ HTTP/1.1" 201 269
INFO 2025-05-21 12:57:06,359 basehttp 62688 6224523264 "GET /admin/user/invitationrecord/ HTTP/1.1" 200 13887
INFO 2025-05-21 12:57:06,405 basehttp 62688 6224523264 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 12:57:15,303 basehttp 62688 6224523264 "GET /admin/user/systemsetting/ HTTP/1.1" 200 12709
INFO 2025-05-21 12:57:15,369 basehttp 62688 6224523264 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 12:57:21,782 basehttp 62688 6224523264 "GET /admin/user/invitationrecord/ HTTP/1.1" 200 13887
INFO 2025-05-21 12:57:21,797 basehttp 62688 6224523264 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 12:57:23,999 basehttp 62688 6224523264 "GET /admin/user/verificationcode/ HTTP/1.1" 200 14316
INFO 2025-05-21 12:57:24,041 basehttp 62688 6224523264 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 12:57:35,021 basehttp 62688 6224523264 "GET /admin/user/invitationrecord/ HTTP/1.1" 200 13887
INFO 2025-05-21 12:57:35,105 basehttp 62688 6224523264 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 12:57:50,602 basehttp 62688 6224523264 "POST /api/auth/generate-invitation-code/ HTTP/1.1" 201 110
INFO 2025-05-21 12:59:09,895 autoreload 62688 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/views.py changed, reloading.
INFO 2025-05-21 12:59:10,451 autoreload 63704 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 12:59:17,201 autoreload 63704 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/serializers.py changed, reloading.
INFO 2025-05-21 12:59:17,903 autoreload 63728 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 12:59:25,108 autoreload 63728 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/models.py changed, reloading.
INFO 2025-05-21 12:59:25,533 autoreload 63738 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 12:59:32,518 autoreload 63738 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/models.py changed, reloading.
INFO 2025-05-21 12:59:33,010 autoreload 63773 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 12:59:53,319 basehttp 63773 6164082688 "POST /api/auth/generate-invitation-code/ HTTP/1.1" 201 110
INFO 2025-05-21 12:59:54,474 basehttp 63773 6164082688 "POST /api/auth/generate-invitation-code/ HTTP/1.1" 201 110
WARNING 2025-05-21 13:00:13,794 log 63773 6164082688 Unauthorized: /api/auth/generate-invitation-code/
WARNING 2025-05-21 13:00:13,795 basehttp 63773 6164082688 "POST /api/auth/generate-invitation-code/ HTTP/1.1" 401 34
INFO 2025-05-21 13:00:26,688 basehttp 63773 6164082688 "GET /api/auth/generate-invitation-code/ HTTP/1.1" 200 109
INFO 2025-05-21 13:00:31,483 basehttp 63773 6164082688 "GET /api/auth/generate-invitation-code/ HTTP/1.1" 200 109
INFO 2025-05-21 13:00:32,255 basehttp 63773 6164082688 "GET /api/auth/generate-invitation-code/ HTTP/1.1" 200 109
INFO 2025-05-21 13:00:32,898 basehttp 63773 6164082688 "GET /api/auth/generate-invitation-code/ HTTP/1.1" 200 109
INFO 2025-05-21 13:01:41,302 basehttp 63773 6180909056 "POST /api/auth/send-code/ HTTP/1.1" 200 51
INFO 2025-05-21 13:02:24,154 basehttp 63773 6180909056 "GET /api/auth/generate-invitation-code/ HTTP/1.1" 200 109
INFO 2025-05-21 13:02:28,480 basehttp 63773 6197735424 "POST /api/auth/register/ HTTP/1.1" 201 268
INFO 2025-05-21 13:02:35,788 basehttp 63773 6197735424 "GET /admin/user/invitationrecord/ HTTP/1.1" 200 14363
INFO 2025-05-21 13:02:35,871 basehttp 63773 6197735424 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:02:49,737 basehttp 63773 6197735424 "GET /admin/user/invitationcode/ HTTP/1.1" 200 18272
INFO 2025-05-21 13:02:49,774 basehttp 63773 6197735424 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:02:50,694 basehttp 63773 6197735424 "GET /admin/user/invitationrecord/ HTTP/1.1" 200 14363
INFO 2025-05-21 13:02:50,714 basehttp 63773 6197735424 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:02:52,774 basehttp 63773 6197735424 "GET /admin/user/invitationcode/ HTTP/1.1" 200 18272
INFO 2025-05-21 13:02:52,793 basehttp 63773 6197735424 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:03:16,498 basehttp 63773 6197735424 "GET /admin/user/invitationrecord/ HTTP/1.1" 200 14363
INFO 2025-05-21 13:03:16,572 basehttp 63773 6197735424 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:03:17,120 basehttp 63773 6197735424 "GET /admin/user/verificationcode/ HTTP/1.1" 200 14790
INFO 2025-05-21 13:03:17,145 basehttp 63773 6197735424 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:03:31,489 basehttp 63773 6197735424 "GET /admin/user/invitationcode/ HTTP/1.1" 200 18272
INFO 2025-05-21 13:03:31,552 basehttp 63773 6197735424 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:03:35,073 basehttp 63773 6197735424 "GET /admin/user/invitationcode/add/ HTTP/1.1" 200 17393
INFO 2025-05-21 13:03:35,173 basehttp 63773 6197735424 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:03:44,767 basehttp 63773 6197735424 "GET /admin/user/invitationcode/ HTTP/1.1" 200 18272
INFO 2025-05-21 13:03:44,822 basehttp 63773 6197735424 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:03:49,452 basehttp 63773 6197735424 "GET /admin/authtoken/tokenproxy/ HTTP/1.1" 200 11541
INFO 2025-05-21 13:03:49,551 basehttp 63773 6197735424 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
INFO 2025-05-21 13:03:51,138 basehttp 63773 6197735424 "GET /admin/user/invitationrecord/ HTTP/1.1" 200 14363
INFO 2025-05-21 13:03:51,157 basehttp 63773 6197735424 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:03:51,908 basehttp 63773 6197735424 "GET /admin/user/verificationcode/ HTTP/1.1" 200 14790
INFO 2025-05-21 13:03:51,927 basehttp 63773 6197735424 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:04:24,645 basehttp 63773 6197735424 "GET /admin/user/systemsetting/ HTTP/1.1" 200 12709
INFO 2025-05-21 13:04:24,946 basehttp 63773 6197735424 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:04:25,486 basehttp 63773 6197735424 "GET /admin/user/user/ HTTP/1.1" 200 15712
INFO 2025-05-21 13:04:25,589 basehttp 63773 6197735424 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:04:33,372 autoreload 63773 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin.py changed, reloading.
INFO 2025-05-21 13:04:34,418 autoreload 64345 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:04:55,507 basehttp 64345 6164705280 "POST /api/auth/login/ HTTP/1.1" 200 295
INFO 2025-05-21 13:04:56,073 basehttp 64345 6164705280 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:04:56,102 basehttp 64345 6164705280 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:04:56,608 basehttp 64345 6164705280 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:04:56,609 basehttp 64345 6198358016 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:04:56,620 basehttp 64345 6215184384 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:04:56,649 basehttp 64345 6198358016 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:04:56,667 basehttp 64345 6198358016 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:04:56,695 basehttp 64345 6198358016 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:04:57,867 autoreload 64345 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin.py changed, reloading.
INFO 2025-05-21 13:04:58,916 autoreload 64395 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:05:00,457 basehttp 64395 6175567872 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:05:00,580 basehttp 64395 6175567872 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:05:04,681 basehttp 64395 6175567872 "GET /admin/user/user/ HTTP/1.1" 200 15712
INFO 2025-05-21 13:05:04,816 basehttp 64395 6175567872 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:05:05,861 basehttp 64395 6175567872 "GET /admin/user/user/ HTTP/1.1" 200 15712
INFO 2025-05-21 13:05:05,939 basehttp 64395 6175567872 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:05:07,486 basehttp 64395 6175567872 "GET /admin/user/user/ HTTP/1.1" 200 15712
INFO 2025-05-21 13:05:07,504 basehttp 64395 6175567872 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:05:13,181 basehttp 64395 6175567872 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:05:13,189 basehttp 64395 6209220608 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:05:13,208 basehttp 64395 6226046976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:05:13,233 basehttp 64395 6209220608 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:05:13,258 basehttp 64395 6209220608 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:05:13,277 basehttp 64395 6209220608 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:05:13,294 basehttp 64395 6209220608 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:05:18,178 basehttp 64395 6226046976 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:05:18,180 basehttp 64395 6175567872 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:05:18,184 basehttp 64395 6209220608 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:05:18,201 basehttp 64395 6209220608 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:05:18,211 basehttp 64395 6209220608 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:05:18,225 basehttp 64395 6209220608 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:05:18,239 basehttp 64395 6209220608 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:05:18,918 autoreload 64395 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin.py changed, reloading.
INFO 2025-05-21 13:05:19,349 autoreload 64441 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:05:22,978 basehttp 64441 6126350336 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3332
INFO 2025-05-21 13:05:23,008 basehttp 64441 6126350336 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3332
INFO 2025-05-21 13:05:25,061 basehttp 64441 6126350336 "GET /admin/user/user/ HTTP/1.1" 200 15709
INFO 2025-05-21 13:05:25,083 basehttp 64441 6126350336 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:05:29,532 basehttp 64441 6160003072 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:05:29,534 basehttp 64441 6126350336 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:05:29,592 basehttp 64441 6176829440 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3332
INFO 2025-05-21 13:05:29,618 basehttp 64441 6160003072 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3332
INFO 2025-05-21 13:05:29,632 basehttp 64441 6160003072 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3332
INFO 2025-05-21 13:05:29,645 basehttp 64441 6160003072 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3332
INFO 2025-05-21 13:05:29,661 basehttp 64441 6160003072 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3332
INFO 2025-05-21 13:05:29,736 basehttp 64441 6160003072 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3332
INFO 2025-05-21 13:05:34,810 basehttp 64441 6126350336 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:05:34,811 basehttp 64441 6176829440 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:05:34,840 basehttp 64441 6160003072 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3332
INFO 2025-05-21 13:05:34,864 basehttp 64441 6160003072 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3332
INFO 2025-05-21 13:05:34,886 basehttp 64441 6160003072 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3332
INFO 2025-05-21 13:05:34,903 basehttp 64441 6160003072 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3332
INFO 2025-05-21 13:05:34,928 basehttp 64441 6160003072 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3332
INFO 2025-05-21 13:05:34,939 basehttp 64441 6160003072 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3332
INFO 2025-05-21 13:05:42,638 basehttp 64441 6126350336 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:05:42,677 basehttp 64441 6126350336 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:05:57,379 basehttp 64441 6160003072 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:05:57,384 basehttp 64441 6126350336 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:05:57,399 basehttp 64441 6176829440 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:05:57,417 basehttp 64441 6160003072 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:05:57,432 basehttp 64441 6160003072 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:05:57,444 basehttp 64441 6160003072 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:05:57,457 basehttp 64441 6160003072 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:05:57,468 basehttp 64441 6160003072 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:05:57,479 basehttp 64441 6160003072 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:06:02,121 basehttp 64441 6126350336 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:06:02,221 basehttp 64441 6126350336 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:06:30,568 autoreload 64441 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin.py changed, reloading.
INFO 2025-05-21 13:06:31,394 autoreload 64602 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:06:40,594 autoreload 64602 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin.py changed, reloading.
INFO 2025-05-21 13:06:41,016 autoreload 64632 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:07:02,247 autoreload 64856 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:07:14,192 basehttp 64632 6167719936 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:07:26,867 basehttp 64856 6170750976 "GET /admin/user/user/points-leaderboard/ HTTP/1.1" 302 0
INFO 2025-05-21 13:07:26,906 basehttp 64856 6170750976 "GET /admin/login/?next=/admin/user/user/points-leaderboard/ HTTP/1.1" 200 4246
INFO 2025-05-21 13:07:27,027 basehttp 64856 12935360512 "GET /static/admin/css/login.css HTTP/1.1" 200 958
INFO 2025-05-21 13:07:27,028 basehttp 64856 12901707776 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2682
INFO 2025-05-21 13:07:27,030 basehttp 64856 12918534144 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
INFO 2025-05-21 13:07:27,030 basehttp 64856 6170750976 "GET /static/admin/css/base.css HTTP/1.1" 200 21544
INFO 2025-05-21 13:07:27,030 basehttp 64856 12952186880 "GET /static/admin/css/responsive.css HTTP/1.1" 200 17905
INFO 2025-05-21 13:07:27,358 basehttp 64856 12952186880 "GET /static/admin/js/theme.js HTTP/1.1" 200 1943
INFO 2025-05-21 13:07:27,358 basehttp 64856 6170750976 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
WARNING 2025-05-21 13:07:27,467 log 64856 6170750976 Not Found: /favicon.ico
WARNING 2025-05-21 13:07:27,468 basehttp 64856 6170750976 "GET /favicon.ico HTTP/1.1" 404 2928
INFO 2025-05-21 13:07:33,679 basehttp 64632 6325039104 "GET /admin/user/user/ HTTP/1.1" 200 16333
INFO 2025-05-21 13:07:33,782 basehttp 64632 6325039104 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:07:41,515 basehttp 64632 6325039104 "GET /admin/user/user/ HTTP/1.1" 200 16333
INFO 2025-05-21 13:07:41,553 basehttp 64632 6325039104 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:08:12,825 basehttp 64632 6325039104 "GET /admin/user/user/ HTTP/1.1" 200 16333
INFO 2025-05-21 13:08:12,857 basehttp 64632 6325039104 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:08:18,683 basehttp 64632 6325039104 "GET /admin/user/user/points-leaderboard/ HTTP/1.1" 200 8667
INFO 2025-05-21 13:08:31,396 basehttp 64632 6325039104 "GET /admin/user/ HTTP/1.1" 200 6901
INFO 2025-05-21 13:08:32,407 basehttp 64632 6325039104 "GET /admin/ HTTP/1.1" 200 10438
INFO 2025-05-21 13:09:21,103 basehttp 64632 6325039104 "GET /admin/ HTTP/1.1" 200 10438
INFO 2025-05-21 13:09:58,646 autoreload 64632 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin.py changed, reloading.
INFO 2025-05-21 13:09:59,099 autoreload 65183 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:10:07,864 autoreload 65183 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin.py changed, reloading.
INFO 2025-05-21 13:10:08,298 autoreload 65205 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:10:21,198 autoreload 65205 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin.py changed, reloading.
INFO 2025-05-21 13:10:21,709 autoreload 65240 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:10:35,462 autoreload 65240 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin.py changed, reloading.
INFO 2025-05-21 13:10:35,944 autoreload 65289 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:10:40,671 basehttp 65289 6174011392 "GET /admin/user/invitationcode/ HTTP/1.1" 200 19372
INFO 2025-05-21 13:10:40,882 basehttp 65289 6174011392 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:10:41,872 basehttp 65289 6174011392 "GET /admin/user/invitationrecord/ HTTP/1.1" 200 14363
INFO 2025-05-21 13:10:41,892 basehttp 65289 6174011392 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:10:42,488 basehttp 65289 6174011392 "GET /admin/user/verificationcode/ HTTP/1.1" 200 14790
INFO 2025-05-21 13:10:42,512 basehttp 65289 6174011392 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:11:14,276 basehttp 65289 6174011392 "GET / HTTP/1.1" 200 32279
INFO 2025-05-21 13:11:14,445 basehttp 65289 6174011392 "GET /static/website/images/binance.ico HTTP/1.1" 200 4286
INFO 2025-05-21 13:11:14,446 basehttp 65289 6207664128 "GET /static/website/images/icon128.png HTTP/1.1" 200 26177
INFO 2025-05-21 13:11:14,468 basehttp 65289 6207664128 "GET /static/website/images/okx.ico HTTP/1.1" 200 2462
INFO 2025-05-21 13:11:14,506 basehttp 65289 6174011392 "GET /static/website/images/gate.ico HTTP/1.1" 200 619
INFO 2025-05-21 13:11:14,507 basehttp 65289 6207664128 "GET /static/website/images/bybit.ico HTTP/1.1" 200 15086
INFO 2025-05-21 13:11:14,591 basehttp 65289 6207664128 "GET /static/website/images/htx.ico HTTP/1.1" 200 1536
INFO 2025-05-21 13:11:16,909 basehttp 65289 6207664128 "GET /static/website/images/favicon.ico HTTP/1.1" 200 4286
INFO 2025-05-21 13:11:17,467 autoreload 65554 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:11:38,845 basehttp 65554 6162067456 "GET /admin/user/ HTTP/1.1" 302 0
INFO 2025-05-21 13:11:38,893 basehttp 65554 6162067456 "GET /admin/login/?next=/admin/user/ HTTP/1.1" 200 4198
INFO 2025-05-21 13:11:49,748 basehttp 65289 6174011392 "GET /admin/user/verificationcode/ HTTP/1.1" 200 14790
INFO 2025-05-21 13:11:52,137 basehttp 65289 6174011392 "GET /admin/user/verificationcode/ HTTP/1.1" 200 14790
INFO 2025-05-21 13:11:52,157 basehttp 65289 6174011392 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:12:07,393 basehttp 65289 6174011392 "GET /admin/user/user/ HTTP/1.1" 200 16333
INFO 2025-05-21 13:12:07,433 basehttp 65289 6174011392 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:12:10,463 basehttp 65289 6174011392 "GET /admin/user/systemsetting/ HTTP/1.1" 200 12976
INFO 2025-05-21 13:12:10,514 basehttp 65289 6174011392 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:12:12,856 basehttp 65289 6174011392 "GET /admin/user/invitationcode/ HTTP/1.1" 200 19372
INFO 2025-05-21 13:12:12,896 basehttp 65289 6174011392 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:12:14,901 basehttp 65289 6174011392 "GET /admin/user/invitationrecord/ HTTP/1.1" 200 14363
INFO 2025-05-21 13:12:14,917 basehttp 65289 6174011392 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:13:30,355 autoreload 65289 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin.py changed, reloading.
INFO 2025-05-21 13:13:31,217 autoreload 65785 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:13:42,824 autoreload 65785 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin.py changed, reloading.
INFO 2025-05-21 13:13:43,276 autoreload 65832 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:13:54,119 autoreload 65832 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin.py changed, reloading.
INFO 2025-05-21 13:13:54,557 autoreload 65854 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:15:06,301 autoreload 65854 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/config/urls.py changed, reloading.
INFO 2025-05-21 13:15:06,980 autoreload 66009 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:15:19,248 autoreload 66197 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:15:40,377 basehttp 66197 6163623936 "GET /admin/user/ HTTP/1.1" 302 0
INFO 2025-05-21 13:15:40,548 basehttp 66197 6163623936 "GET /admin/login/?next=/admin/user/ HTTP/1.1" 200 4193
WARNING 2025-05-21 13:15:47,000 log 66009 6204469248 Not Found: /admin/user/invitationrecord/
WARNING 2025-05-21 13:15:47,001 basehttp 66009 6204469248 "GET /admin/user/invitationrecord/ HTTP/1.1" 404 3839
WARNING 2025-05-21 13:15:48,280 log 66009 6204469248 Not Found: /admin/user/invitationrecord/
WARNING 2025-05-21 13:15:48,281 basehttp 66009 6204469248 "GET /admin/user/invitationrecord/ HTTP/1.1" 404 3839
WARNING 2025-05-21 13:15:49,791 log 66009 6204469248 Not Found: /admin/user/invitationcode/
WARNING 2025-05-21 13:15:49,792 basehttp 66009 6204469248 "GET /admin/user/invitationcode/ HTTP/1.1" 404 3833
INFO 2025-05-21 13:15:53,780 basehttp 66009 6204469248 "GET / HTTP/1.1" 200 32279
INFO 2025-05-21 13:16:01,163 basehttp 66009 6204469248 "GET /admin/ HTTP/1.1" 200 4510
INFO 2025-05-21 13:16:05,817 basehttp 66009 6204469248 "GET /admin/ HTTP/1.1" 200 4510
INFO 2025-05-21 13:16:30,303 basehttp 66009 6204469248 "POST /admin/logout/ HTTP/1.1" 200 3569
INFO 2025-05-21 13:16:31,672 basehttp 66009 6204469248 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-05-21 13:16:31,691 basehttp 66009 6204469248 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4183
INFO 2025-05-21 13:16:31,702 basehttp 66009 6204469248 "GET /static/admin/css/login.css HTTP/1.1" 304 0
INFO 2025-05-21 13:16:37,750 basehttp 66009 6204469248 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-05-21 13:16:37,762 basehttp 66009 6204469248 "GET /admin/ HTTP/1.1" 200 4510
INFO 2025-05-21 13:17:12,691 autoreload 66009 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin_site.py changed, reloading.
INFO 2025-05-21 13:17:13,227 autoreload 66413 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:17:23,123 autoreload 66413 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/config/urls.py changed, reloading.
INFO 2025-05-21 13:17:23,519 autoreload 66435 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:17:50,884 autoreload 66655 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:17:55,748 basehttp 66435 6161428480 "GET /admin/ HTTP/1.1" 200 10438
INFO 2025-05-21 13:18:02,030 basehttp 66435 6161428480 "GET /admin/user/user/ HTTP/1.1" 200 15709
INFO 2025-05-21 13:18:02,069 basehttp 66435 6161428480 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:18:05,447 basehttp 66435 6161428480 "GET /admin/user/systemsetting/ HTTP/1.1" 200 12709
INFO 2025-05-21 13:18:05,478 basehttp 66435 6161428480 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:18:06,331 basehttp 66435 6161428480 "GET /admin/user/invitationcode/ HTTP/1.1" 200 18272
INFO 2025-05-21 13:18:06,361 basehttp 66435 6161428480 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:18:08,528 basehttp 66435 6161428480 "GET /admin/user/invitationrecord/ HTTP/1.1" 200 14363
INFO 2025-05-21 13:18:08,551 basehttp 66435 6161428480 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:18:11,458 basehttp 66435 6161428480 "GET /admin/user/verificationcode/ HTTP/1.1" 200 14790
INFO 2025-05-21 13:18:11,487 basehttp 66435 6161428480 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:18:12,971 basehttp 66435 6161428480 "GET /admin/user/invitationcode/ HTTP/1.1" 200 18272
INFO 2025-05-21 13:18:13,010 basehttp 66435 6161428480 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:18:13,347 basehttp 66435 6161428480 "GET /admin/user/systemsetting/ HTTP/1.1" 200 12709
INFO 2025-05-21 13:18:13,367 basehttp 66435 6161428480 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:18:14,095 basehttp 66435 6161428480 "GET /admin/user/user/ HTTP/1.1" 200 15709
INFO 2025-05-21 13:18:14,112 basehttp 66435 6161428480 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:18:14,408 basehttp 66655 6167900160 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-05-21 13:18:14,460 basehttp 66655 6167900160 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4188
INFO 2025-05-21 13:18:18,604 basehttp 66435 6161428480 "GET /admin/user/user/ HTTP/1.1" 200 15709
INFO 2025-05-21 13:18:18,627 basehttp 66435 6161428480 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:18:20,701 basehttp 66435 6161428480 "GET /admin/CryptoAnalyst/token/ HTTP/1.1" 200 34868
INFO 2025-05-21 13:18:20,719 basehttp 66435 6161428480 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:18:21,918 basehttp 66435 6161428480 "GET /admin/CryptoAnalyst/chain/ HTTP/1.1" 200 11999
INFO 2025-05-21 13:18:21,937 basehttp 66435 6161428480 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:18:23,195 basehttp 66435 6161428480 "GET /admin/user/user/ HTTP/1.1" 200 15709
INFO 2025-05-21 13:18:23,230 basehttp 66435 6161428480 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:18:26,753 basehttp 66435 6161428480 "GET /admin/user/systemsetting/ HTTP/1.1" 200 12709
INFO 2025-05-21 13:18:26,795 basehttp 66435 6161428480 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:18:27,494 basehttp 66435 6161428480 "GET /admin/user/invitationcode/ HTTP/1.1" 200 18272
INFO 2025-05-21 13:18:27,509 basehttp 66435 6161428480 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:18:28,182 basehttp 66435 6161428480 "GET /admin/user/invitationrecord/ HTTP/1.1" 200 14363
INFO 2025-05-21 13:18:28,197 basehttp 66435 6161428480 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:18:28,740 basehttp 66435 6161428480 "GET /admin/user/verificationcode/ HTTP/1.1" 200 14790
INFO 2025-05-21 13:18:28,758 basehttp 66435 6161428480 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:19:12,675 autoreload 66435 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin.py changed, reloading.
INFO 2025-05-21 13:19:13,209 autoreload 66831 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:19:28,165 autoreload 66831 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin.py changed, reloading.
INFO 2025-05-21 13:19:28,861 autoreload 66866 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:19:50,000 autoreload 66866 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin.py changed, reloading.
INFO 2025-05-21 13:19:50,866 autoreload 66935 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:20:00,845 autoreload 66935 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin.py changed, reloading.
INFO 2025-05-21 13:20:01,489 autoreload 66964 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:21:48,949 autoreload 66964 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/admin.py changed, reloading.
INFO 2025-05-21 13:21:49,857 autoreload 67165 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:21:59,131 autoreload 67344 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 13:22:23,761 basehttp 67344 6171095040 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-05-21 13:22:23,861 basehttp 67344 6171095040 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4188
INFO 2025-05-21 13:22:43,283 basehttp 67165 6194442240 "GET /admin/user/verificationcode/ HTTP/1.1" 200 14790
INFO 2025-05-21 13:22:43,343 basehttp 67165 6194442240 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:22:44,329 basehttp 67165 6194442240 "GET /admin/user/verificationcode/ HTTP/1.1" 200 14790
INFO 2025-05-21 13:22:44,364 basehttp 67165 6194442240 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:23:11,203 basehttp 67165 6194442240 "GET /admin/user/user/ HTTP/1.1" 200 15709
INFO 2025-05-21 13:23:11,228 basehttp 67165 6194442240 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:23:15,038 basehttp 67165 6194442240 "GET /admin/user/user/ HTTP/1.1" 200 15709
INFO 2025-05-21 13:23:15,058 basehttp 67165 6194442240 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:23:16,199 basehttp 67165 6194442240 "GET /admin/user/user/ HTTP/1.1" 200 15709
INFO 2025-05-21 13:23:16,211 basehttp 67165 6194442240 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:23:16,392 basehttp 67165 6194442240 "GET /admin/user/user/ HTTP/1.1" 200 15709
INFO 2025-05-21 13:23:16,405 basehttp 67165 6194442240 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:23:20,298 basehttp 67165 6194442240 "GET /admin/user/systemsetting/ HTTP/1.1" 200 12709
INFO 2025-05-21 13:23:20,330 basehttp 67165 6194442240 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:23:21,832 basehttp 67165 6194442240 "GET /admin/user/invitationcode/ HTTP/1.1" 200 18272
INFO 2025-05-21 13:23:21,849 basehttp 67165 6194442240 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:23:24,672 basehttp 67165 6194442240 "GET /admin/user/user/ HTTP/1.1" 200 15709
INFO 2025-05-21 13:23:24,690 basehttp 67165 6194442240 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:23:27,089 basehttp 67165 6194442240 "GET /admin/user/user/?o=5 HTTP/1.1" 200 16177
INFO 2025-05-21 13:23:27,411 basehttp 67165 6194442240 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:23:28,366 basehttp 67165 6194442240 "GET /admin/user/user/?o=-5 HTTP/1.1" 200 16197
INFO 2025-05-21 13:23:28,381 basehttp 67165 6194442240 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 13:25:12,580 basehttp 67165 6211268608 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:25:12,586 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:25:12,634 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:25:12,647 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:25:12,662 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:25:12,674 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:25:12,687 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:25:12,699 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:25:12,711 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:25:12,721 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:25:14,490 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:25:14,511 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:25:17,240 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:25:17,259 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:25:17,267 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:25:17,290 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:25:17,305 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:25:17,316 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:25:17,334 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:25:17,350 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:25:17,363 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:25:17,375 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:25:17,389 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 3051
INFO 2025-05-21 13:29:04,344 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:29:04,349 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:29:21,865 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:29:21,867 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:29:38,041 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:29:38,041 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:29:54,120 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:29:54,121 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:30:15,722 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:30:15,726 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:31:23,624 basehttp 67165 6228094976 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:31:23,625 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:31:30,942 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:31:30,979 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:31:32,008 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:31:32,018 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:31:32,028 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:31:36,133 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:31:36,224 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:31:37,530 basehttp 67165 6194442240 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:32:06,613 basehttp 67165 6194442240 "POST /api/auth/login/ HTTP/1.1" 200 295
INFO 2025-05-21 13:32:07,170 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:32:07,196 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:32:12,059 basehttp 67165 6194442240 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:32:15,050 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:32:15,078 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:32:15,680 basehttp 67165 6194442240 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:32:21,606 basehttp 67165 6194442240 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 13:32:21,698 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:32:21,712 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:32:21,742 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:32:23,344 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:32:24,379 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:32:24,413 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:32:25,374 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:32:28,148 basehttp 67165 6194442240 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 13:32:28,207 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:32:28,215 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:32:28,237 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:32:28,254 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:32:30,990 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:32:31,074 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:32:32,360 basehttp 67165 6194442240 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:32:35,621 basehttp 67165 6194442240 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 13:32:38,052 basehttp 67165 6194442240 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 13:32:45,091 basehttp 67165 6194442240 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 13:32:52,411 basehttp 67165 6194442240 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 13:32:56,047 basehttp 67165 6194442240 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 13:33:00,297 basehttp 67165 6194442240 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 13:33:04,709 basehttp 67165 6194442240 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 13:33:06,877 basehttp 67165 6194442240 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 13:33:12,730 basehttp 67165 6194442240 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 13:33:21,753 basehttp 67165 6194442240 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:33:23,477 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:33:23,480 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:33:24,524 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:33:25,790 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:33:25,821 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:33:26,385 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:33:26,389 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:33:26,395 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:33:28,329 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:33:28,336 basehttp 67165 6244921344 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:33:28,342 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:33:29,487 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:33:29,942 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:33:29,944 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:33:29,951 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:33:30,601 basehttp 67165 6194442240 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:33:31,061 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:33:31,062 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:33:31,068 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:33:32,241 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:33:32,260 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:33:32,799 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:33:33,687 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:33:33,692 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:33:33,696 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:33:33,709 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:33:34,616 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:33:35,924 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:33:35,940 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:33:36,608 basehttp 67165 6194442240 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:33:37,527 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:33:37,537 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:33:37,541 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:33:37,553 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:33:37,569 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:33:38,350 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:33:48,062 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:33:48,091 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:33:49,422 basehttp 67165 6194442240 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:35:04,479 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:35:04,480 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:35:04,509 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:35:04,668 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:35:04,733 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:35:04,767 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:35:05,109 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:35:07,066 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:35:07,077 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:35:07,084 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:35:07,110 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:35:07,130 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:35:07,147 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:35:07,520 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:35:35,981 basehttp 67165 6194442240 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:35:42,160 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:35:42,200 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:35:42,223 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:35:42,269 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:35:42,299 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:35:42,347 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:35:48,910 basehttp 67165 6194442240 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 13:35:48,967 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:35:53,826 basehttp 67165 6194442240 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:35:55,075 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:35:55,102 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:35:56,920 basehttp 67165 6194442240 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:36:01,020 basehttp 67165 6194442240 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 13:36:01,093 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:36:01,101 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:36:02,296 basehttp 67165 6194442240 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:36:05,738 basehttp 67165 6194442240 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:36:07,045 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:36:07,053 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:36:07,062 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:36:07,083 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:36:07,098 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:36:07,110 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:36:07,899 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:36:10,391 basehttp 67165 6228094976 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:36:10,395 basehttp 67165 6194442240 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:36:10,403 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:36:10,415 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:36:10,429 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:36:10,445 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:36:24,720 basehttp 67165 6194442240 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:36:28,480 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:36:28,530 basehttp 67165 6194442240 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:36:29,261 basehttp 67165 6194442240 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:37:35,372 basehttp 67165 6194442240 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:38:00,087 basehttp 67165 6228094976 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 13:38:08,442 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:38:13,424 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:38:13,468 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:38:14,672 basehttp 67165 6244921344 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:38:14,675 basehttp 67165 6228094976 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:38:14,679 basehttp 67165 6261747712 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:38:18,886 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:38:22,159 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:38:41,599 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:38:42,314 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:39:18,599 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:39:18,669 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:39:19,668 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:40:20,094 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:40:20,134 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:40:21,963 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:40:23,618 basehttp 67165 6244921344 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:40:23,623 basehttp 67165 6228094976 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:40:23,632 basehttp 67165 6261747712 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:40:24,405 basehttp 67165 6244921344 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:42:05,727 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:42:46,624 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:42:46,658 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 13:42:52,529 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:43:00,660 basehttp 67165 6228094976 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 13:43:07,679 basehttp 67165 6228094976 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 13:43:19,923 basehttp 67165 6228094976 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 13:43:32,683 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:45:59,071 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:49:25,600 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:49:38,693 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 13:54:42,932 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 13:54:42,965 log 67165 6228094976 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 13:54:42,965 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 13:55:02,152 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 13:55:02,176 log 67165 6228094976 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 13:55:02,176 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 13:55:15,051 basehttp 67165 6244921344 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:55:15,066 basehttp 67165 6228094976 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:55:16,873 basehttp 67165 6244921344 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:55:16,873 basehttp 67165 6228094976 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:55:18,819 basehttp 67165 6244921344 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 13:55:18,836 log 67165 6244921344 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 13:55:18,837 basehttp 67165 6244921344 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 13:58:30,043 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 13:58:30,076 log 67165 6228094976 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 13:58:30,077 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 13:58:57,406 basehttp 67165 6244921344 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 13:58:57,413 basehttp 67165 6228094976 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 13:58:59,067 basehttp 67165 6244921344 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 13:58:59,076 log 67165 6244921344 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 13:58:59,076 basehttp 67165 6244921344 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 13:59:03,042 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 13:59:03,086 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:00:14,459 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:00:14,599 log 67165 6228094976 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:00:14,599 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:01:24,574 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:01:24,591 log 67165 6228094976 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:01:24,592 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:02:26,368 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:02:26,441 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:02:26,528 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:02:26,557 log 67165 6228094976 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:02:26,557 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:02:43,312 basehttp 67165 6244921344 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 14:02:43,315 basehttp 67165 6228094976 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 14:02:43,326 basehttp 67165 6261747712 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:02:44,883 basehttp 67165 6244921344 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:02:44,897 log 67165 6244921344 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:02:44,897 basehttp 67165 6244921344 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:02:46,682 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:02:46,707 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:02:46,806 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:02:46,815 log 67165 6228094976 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:02:46,816 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:03:58,506 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:03:58,533 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:04:04,780 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:04:04,805 log 67165 6228094976 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:04:04,805 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:05:01,513 basehttp 67165 6244921344 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 14:05:01,561 basehttp 67165 6228094976 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 14:05:01,571 basehttp 67165 6261747712 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:05:01,590 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:05:01,616 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:05:02,944 basehttp 67165 6244921344 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:05:02,958 log 67165 6244921344 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:05:02,958 basehttp 67165 6244921344 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:05:04,761 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:05:04,799 basehttp 67165 6244921344 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:05:05,668 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:05:05,680 log 67165 6228094976 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:05:05,681 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:09:54,939 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:09:54,974 log 67165 6228094976 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:09:54,975 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:10:04,258 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:10:04,294 log 67165 6228094976 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:10:04,294 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:11:53,423 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:11:53,460 basehttp 67165 6228094976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:12:01,172 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:12:01,187 log 67165 6228094976 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:12:01,188 basehttp 67165 6228094976 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:14:50,547 autoreload 85389 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 14:15:15,256 basehttp 85389 6168211456 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:15:15,311 log 85389 6168211456 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:15:15,311 basehttp 85389 6168211456 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:15:54,578 basehttp 85389 6168211456 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:15:54,620 log 85389 6168211456 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:15:54,620 basehttp 85389 6168211456 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:16:05,861 basehttp 85389 6168211456 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:16:05,876 log 85389 6168211456 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:16:05,876 basehttp 85389 6168211456 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:16:15,193 basehttp 85389 6168211456 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:16:15,270 log 85389 6168211456 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:16:15,272 basehttp 85389 6168211456 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:16:25,356 basehttp 85389 6168211456 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:16:25,396 log 85389 6168211456 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:16:25,396 basehttp 85389 6168211456 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:16:30,228 basehttp 85389 6185037824 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 14:16:30,240 basehttp 85389 6168211456 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 14:16:31,503 basehttp 85389 6168211456 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:16:31,513 log 85389 6168211456 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:16:31,514 basehttp 85389 6168211456 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:16:33,830 basehttp 85389 6168211456 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:16:33,849 basehttp 85389 6168211456 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:16:34,308 basehttp 85389 6168211456 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:16:34,319 log 85389 6168211456 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:16:34,319 basehttp 85389 6168211456 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:16:40,690 basehttp 85389 6201864192 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 14:16:40,702 basehttp 85389 6168211456 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 14:16:40,710 basehttp 85389 6218690560 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:16:42,323 basehttp 85389 6168211456 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 14:16:42,323 basehttp 85389 6218690560 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 14:16:42,331 basehttp 85389 6201864192 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:16:44,216 basehttp 85389 6201864192 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:16:44,236 log 85389 6201864192 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:16:44,237 basehttp 85389 6201864192 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
WARNING 2025-05-21 14:17:24,195 log 85389 6168211456 Unauthorized: /api/auth/generate-invitation-code/
WARNING 2025-05-21 14:17:24,196 basehttp 85389 6168211456 "POST /api/auth/generate-invitation-code/ HTTP/1.1" 401 34
INFO 2025-05-21 14:17:47,970 basehttp 85389 6168211456 "POST /api/auth/generate-invitation-code/ HTTP/1.1" 201 110
INFO 2025-05-21 14:17:54,023 basehttp 85389 6168211456 "POST /api/auth/generate-invitation-code/ HTTP/1.1" 201 110
INFO 2025-05-21 14:18:02,000 basehttp 85389 6168211456 "GET /api/auth/generate-invitation-code/ HTTP/1.1" 200 109
INFO 2025-05-21 14:18:10,494 basehttp 85389 6168211456 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 14:20:22,021 basehttp 85389 6201864192 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:20:22,096 log 85389 6201864192 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:20:22,097 basehttp 85389 6201864192 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
INFO 2025-05-21 14:20:33,301 basehttp 85389 6201864192 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:20:33,323 log 85389 6201864192 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:20:33,323 basehttp 85389 6201864192 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4855
WARNING 2025-05-21 14:26:48,289 log 85389 6201864192 Not Found: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:26:48,291 basehttp 85389 6201864192 "POST /api/auth/invitation-info/ranking/ HTTP/1.1" 404 4856
INFO 2025-05-21 14:28:15,501 autoreload 85389 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/views.py changed, reloading.
INFO 2025-05-21 14:28:16,138 autoreload 90733 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 14:28:21,186 autoreload 90733 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/urls.py changed, reloading.
INFO 2025-05-21 14:28:21,951 autoreload 90757 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 14:28:58,039 autoreload 90757 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/views.py changed, reloading.
INFO 2025-05-21 14:28:58,452 autoreload 90824 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 14:29:05,721 autoreload 90824 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/urls.py changed, reloading.
INFO 2025-05-21 14:29:06,083 autoreload 90853 8462421120 Watching for file changes with StatReloader
WARNING 2025-05-21 14:29:12,965 log 90853 6132871168 Unauthorized: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:29:12,967 basehttp 90853 6132871168 "POST /api/auth/invitation-info/ranking/ HTTP/1.1" 401 34
WARNING 2025-05-21 14:29:27,352 log 90853 6132871168 Method Not Allowed: /api/auth/invitation-info/ranking/
WARNING 2025-05-21 14:29:27,353 basehttp 90853 6132871168 "POST /api/auth/invitation-info/ranking/ HTTP/1.1" 405 46
INFO 2025-05-21 14:29:32,073 basehttp 90853 6132871168 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-21 14:29:47,454 basehttp 90853 6149697536 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 14:29:47,471 basehttp 90853 6149697536 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-21 14:32:21,314 basehttp 90853 6149697536 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 14:32:21,372 basehttp 90853 6149697536 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-21 14:34:37,280 basehttp 90853 6149697536 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 14:34:37,535 basehttp 90853 6149697536 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-21 14:35:48,482 basehttp 90853 6166523904 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 14:35:48,540 basehttp 90853 6149697536 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 14:35:49,766 basehttp 90853 6149697536 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 14:35:49,775 basehttp 90853 6166523904 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 14:35:50,983 basehttp 90853 6166523904 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 14:35:50,994 basehttp 90853 6166523904 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-21 14:40:46,609 autoreload 90853 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/website/views.py changed, reloading.
INFO 2025-05-21 14:40:47,271 autoreload 92041 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 14:42:08,566 autoreload 92041 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/models.py changed, reloading.
INFO 2025-05-21 14:42:09,053 autoreload 92180 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 14:42:16,929 autoreload 92180 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/website/views.py changed, reloading.
INFO 2025-05-21 14:42:17,340 autoreload 92214 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 14:42:27,246 autoreload 92214 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/views.py changed, reloading.
INFO 2025-05-21 14:42:27,649 autoreload 92229 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 14:42:34,418 autoreload 92229 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/urls.py changed, reloading.
INFO 2025-05-21 14:42:34,816 autoreload 92256 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 14:42:49,641 autoreload 92256 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/views.py changed, reloading.
INFO 2025-05-21 14:42:50,250 autoreload 92279 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 14:43:27,342 autoreload 92398 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 14:44:33,840 basehttp 92398 6166114304 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
INFO 2025-05-21 14:44:33,882 basehttp 92398 12901707776 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
WARNING 2025-05-21 14:44:33,901 log 92398 6166114304 Not Found: /api/auth/claim-temporary-invitation/
WARNING 2025-05-21 14:44:33,902 basehttp 92398 6166114304 "POST /api/auth/claim-temporary-invitation/ HTTP/1.1" 404 58
WARNING 2025-05-21 14:46:04,502 log 92398 6166114304 Method Not Allowed: /api/auth/claim-temporary-invitation/
WARNING 2025-05-21 14:46:04,503 basehttp 92398 6166114304 "GET /api/auth/claim-temporary-invitation/ HTTP/1.1" 405 45
WARNING 2025-05-21 14:46:08,724 log 92398 6166114304 Not Found: /api/auth/claim-temporary-invitation/
WARNING 2025-05-21 14:46:08,725 basehttp 92398 6166114304 "POST /api/auth/claim-temporary-invitation/ HTTP/1.1" 404 58
INFO 2025-05-21 14:46:30,899 basehttp 92398 12901707776 "GET /api/auth/invitation-info/ HTTP/1.1" 200 417
WARNING 2025-05-21 14:46:30,924 log 92398 12901707776 Not Found: /api/auth/claim-temporary-invitation/
INFO 2025-05-21 14:46:30,926 basehttp 92398 12918534144 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
WARNING 2025-05-21 14:46:30,926 basehttp 92398 12901707776 "POST /api/auth/claim-temporary-invitation/ HTTP/1.1" 404 58
INFO 2025-05-21 14:47:18,831 basehttp 92398 12918534144 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 14:47:18,836 basehttp 92398 12901707776 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 14:47:36,492 basehttp 92398 12901707776 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:47:36,568 basehttp 92398 12901707776 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 14:47:38,408 basehttp 92398 12935360512 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 14:47:38,421 basehttp 92398 12901707776 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 14:47:38,442 basehttp 92398 12952186880 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
WARNING 2025-05-21 14:48:58,714 log 92398 12901707776 Not Found: /api/auth/claim-temporary-invitation/
WARNING 2025-05-21 14:48:58,715 basehttp 92398 12901707776 "POST /api/auth/claim-temporary-invitation/ HTTP/1.1" 404 58
INFO 2025-05-21 14:49:14,500 basehttp 92398 12935360512 "GET /admin/user/user/ HTTP/1.1" 200 15712
INFO 2025-05-21 14:49:14,630 basehttp 92398 12935360512 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 14:49:23,868 basehttp 92398 12935360512 "POST /admin/user/user/ HTTP/1.1" 200 11484
INFO 2025-05-21 14:49:23,981 basehttp 92398 12935360512 "GET /static/admin/js/cancel.js HTTP/1.1" 200 884
INFO 2025-05-21 14:49:25,299 basehttp 92398 12935360512 "POST /admin/user/user/ HTTP/1.1" 302 0
INFO 2025-05-21 14:49:25,328 basehttp 92398 12935360512 "GET /admin/user/user/ HTTP/1.1" 200 14256
INFO 2025-05-21 14:49:25,368 basehttp 92398 12935360512 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-21 14:49:42,562 basehttp 92398 12935360512 "POST /api/auth/send-code/ HTTP/1.1" 200 51
INFO 2025-05-21 14:53:04,003 basehttp 92398 12935360512 "GET /?code=U1W9NUAX HTTP/1.1" 200 32279
INFO 2025-05-21 14:54:05,942 autoreload 92398 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/website/views.py changed, reloading.
INFO 2025-05-21 14:54:07,093 autoreload 93757 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 14:54:26,173 basehttp 93757 6206255104 "GET /?code=U1W9NUAX HTTP/1.1" 200 32279
WARNING 2025-05-21 14:54:56,034 log 93757 6206255104 Not Found: /api/auth/claim-temporary-invitation/
WARNING 2025-05-21 14:54:56,036 basehttp 93757 6206255104 "POST /api/auth/claim-temporary-invitation/ HTTP/1.1" 404 58
INFO 2025-05-21 14:55:51,150 autoreload 93757 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/models.py changed, reloading.
INFO 2025-05-21 14:55:51,855 autoreload 93972 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 14:56:02,161 autoreload 93972 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/website/views.py changed, reloading.
INFO 2025-05-21 14:56:02,772 autoreload 94028 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 14:56:12,856 autoreload 94028 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/views.py changed, reloading.
INFO 2025-05-21 14:56:13,260 autoreload 94058 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 14:56:28,190 autoreload 94058 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/views.py changed, reloading.
INFO 2025-05-21 14:56:28,774 autoreload 94083 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 14:58:11,863 autoreload 94309 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 14:58:15,899 basehttp 94309 6198276096 "GET /?code=U1W9NUAX HTTP/1.1" 200 32279
WARNING 2025-05-21 14:58:31,051 log 94309 6198276096 Bad Request: /api/auth/claim-temporary-invitation/
WARNING 2025-05-21 14:58:31,051 basehttp 94309 6198276096 "POST /api/auth/claim-temporary-invitation/ HTTP/1.1" 400 57
WARNING 2025-05-21 14:59:08,141 log 94309 6198276096 Bad Request: /api/auth/claim-temporary-invitation/
WARNING 2025-05-21 14:59:08,142 basehttp 94309 6198276096 "POST /api/auth/claim-temporary-invitation/ HTTP/1.1" 400 57
WARNING 2025-05-21 15:01:50,655 log 94309 6215102464 Bad Request: /api/auth/claim-temporary-invitation/
WARNING 2025-05-21 15:01:50,656 basehttp 94309 6215102464 "POST /api/auth/claim-temporary-invitation/ HTTP/1.1" 400 57
INFO 2025-05-21 15:02:29,485 autoreload 94309 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/views.py changed, reloading.
INFO 2025-05-21 15:02:30,038 autoreload 94770 8462421120 Watching for file changes with StatReloader
INFO 2025-05-21 15:02:52,239 autoreload 94770 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/user/views.py changed, reloading.
INFO 2025-05-21 15:02:52,791 autoreload 94823 8462421120 Watching for file changes with StatReloader
WARNING 2025-05-21 15:03:02,081 log 94823 6134771712 Bad Request: /api/auth/claim-temporary-invitation/
WARNING 2025-05-21 15:03:02,081 basehttp 94823 6134771712 "POST /api/auth/claim-temporary-invitation/ HTTP/1.1" 400 49
INFO 2025-05-21 15:04:09,888 basehttp 94823 6151598080 "GET /?code=U1W9NUAX HTTP/1.1" 200 32279
WARNING 2025-05-21 15:04:32,301 log 94823 6151598080 Bad Request: /api/auth/claim-temporary-invitation/
WARNING 2025-05-21 15:04:32,303 basehttp 94823 6151598080 "POST /api/auth/claim-temporary-invitation/ HTTP/1.1" 400 49
INFO 2025-05-21 15:07:01,661 basehttp 94823 6168424448 "GET /?code=U1W9NUAX HTTP/1.1" 200 32279
INFO 2025-05-21 15:07:01,794 basehttp 94823 12901707776 "GET /static/website/images/binance.ico HTTP/1.1" 304 0
INFO 2025-05-21 15:07:01,822 basehttp 94823 6168424448 "GET /static/website/images/icon128.png HTTP/1.1" 200 26177
INFO 2025-05-21 15:07:02,248 basehttp 94823 6168424448 "GET /static/website/images/gate.ico HTTP/1.1" 304 0
INFO 2025-05-21 15:07:02,252 basehttp 94823 12901707776 "GET /static/website/images/okx.ico HTTP/1.1" 304 0
INFO 2025-05-21 15:07:02,285 basehttp 94823 6168424448 "GET /static/website/images/htx.ico HTTP/1.1" 304 0
INFO 2025-05-21 15:07:02,285 basehttp 94823 12901707776 "GET /static/website/images/bybit.ico HTTP/1.1" 200 15086
INFO 2025-05-21 15:07:02,378 basehttp 94823 12901707776 "GET /static/website/images/favicon.ico HTTP/1.1" 304 0
WARNING 2025-05-21 15:07:15,637 log 94823 6168424448 Bad Request: /api/auth/claim-temporary-invitation/
WARNING 2025-05-21 15:07:15,637 basehttp 94823 6168424448 "POST /api/auth/claim-temporary-invitation/ HTTP/1.1" 400 49
WARNING 2025-05-21 15:07:35,420 log 94823 6168424448 Unauthorized: /api/auth/claim-temporary-invitation/
WARNING 2025-05-21 15:07:35,420 basehttp 94823 6168424448 "POST /api/auth/claim-temporary-invitation/ HTTP/1.1" 401 43
INFO 2025-05-21 15:08:10,239 basehttp 94823 12901707776 "POST /api/auth/send-code/ HTTP/1.1" 200 51
WARNING 2025-05-21 15:10:10,747 log 94823 12901707776 Bad Request: /api/auth/register/
WARNING 2025-05-21 15:10:10,748 basehttp 94823 12901707776 "POST /api/auth/register/ HTTP/1.1" 400 69
INFO 2025-05-21 15:10:18,499 basehttp 94823 12901707776 "POST /api/auth/send-code/ HTTP/1.1" 200 51
INFO 2025-05-21 15:10:32,421 basehttp 94823 12901707776 "POST /api/auth/register/ HTTP/1.1" 201 268
INFO 2025-05-21 15:10:41,899 basehttp 94823 12901707776 "POST /api/auth/login/ HTTP/1.1" 200 303
INFO 2025-05-21 15:10:42,489 basehttp 94823 12901707776 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 15:10:42,529 basehttp 94823 12901707776 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 15:10:43,779 basehttp 94823 12901707776 "GET /api/auth/invitation-info/ HTTP/1.1" 200 146
INFO 2025-05-21 15:10:43,796 basehttp 94823 12901707776 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-21 15:11:00,087 basehttp 94823 12901707776 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-21 15:11:00,139 basehttp 94823 12935360512 "GET /api/auth/profile/ HTTP/1.1" 200 243
INFO 2025-05-21 15:11:00,142 basehttp 94823 12901707776 "PUT /api/auth/profile/ HTTP/1.1" 200 268
INFO 2025-05-21 15:11:00,148 basehttp 94823 12952186880 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 15:11:02,278 basehttp 94823 12901707776 "GET /api/auth/invitation-info/ HTTP/1.1" 200 146
INFO 2025-05-21 15:11:02,294 basehttp 94823 12901707776 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-21 15:11:05,593 basehttp 94823 12901707776 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 15:11:05,674 basehttp 94823 12901707776 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 15:11:39,416 basehttp 94823 12935360512 "GET /api/auth/profile/ HTTP/1.1" 200 243
INFO 2025-05-21 15:11:39,417 basehttp 94823 12901707776 "PUT /api/auth/profile/ HTTP/1.1" 200 268
INFO 2025-05-21 15:11:39,570 basehttp 94823 12952186880 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 15:11:39,611 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 15:11:47,468 basehttp 94823 12901707776 "POST /api/auth/login/ HTTP/1.1" 200 295
INFO 2025-05-21 15:11:48,029 basehttp 94823 12901707776 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:11:48,069 basehttp 94823 12901707776 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:11:48,659 basehttp 94823 12901707776 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-21 15:11:48,675 basehttp 94823 12901707776 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-21 15:12:08,051 basehttp 94823 12935360512 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 15:12:08,053 basehttp 94823 12901707776 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 15:12:08,060 basehttp 94823 12952186880 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:12:08,072 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:12:08,089 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:12:10,097 basehttp 94823 12935360512 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-21 15:12:10,109 basehttp 94823 12935360512 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-21 15:12:13,406 basehttp 94823 12901707776 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:12:13,439 basehttp 94823 12901707776 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:12:14,746 basehttp 94823 12901707776 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-21 15:12:14,763 basehttp 94823 12901707776 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-21 15:12:15,260 basehttp 94823 12935360512 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 15:12:15,269 basehttp 94823 12901707776 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 15:12:15,286 basehttp 94823 12952186880 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:12:15,300 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:12:15,320 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:12:15,336 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:12:18,408 basehttp 94823 12901707776 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-21 15:12:18,425 basehttp 94823 12901707776 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-21 15:12:32,168 basehttp 94823 12935360512 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 15:12:32,176 basehttp 94823 12901707776 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 15:12:32,191 basehttp 94823 12952186880 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:12:32,226 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:12:32,250 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:12:32,269 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:12:34,560 basehttp 94823 12901707776 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 15:12:34,573 basehttp 94823 12952186880 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 15:12:34,592 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:12:34,665 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:12:34,740 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:12:34,772 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2471
INFO 2025-05-21 15:12:35,576 basehttp 94823 12935360512 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-21 15:12:35,595 basehttp 94823 12935360512 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-21 15:20:38,755 basehttp 94823 12935360512 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-21 15:20:38,794 basehttp 94823 12901707776 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 15:20:38,802 basehttp 94823 12952186880 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 15:20:38,830 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 15:20:38,849 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 15:20:38,877 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 15:20:40,289 basehttp 94823 12952186880 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 15:20:40,291 basehttp 94823 12901707776 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-21 15:20:40,300 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 15:20:40,381 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 15:20:40,471 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 15:20:40,499 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 1850
INFO 2025-05-21 15:20:41,191 basehttp 94823 12935360512 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-21 15:20:41,201 basehttp 94823 12935360512 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 04:52:52,413 basehttp 94823 12901707776 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
ERROR 2025-05-22 04:52:52,491 log 94823 12952186880 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-22 04:52:52,491 basehttp 94823 12952186880 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 500 147
INFO 2025-05-22 04:52:52,505 basehttp 94823 12935360512 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 04:52:52,784 basehttp 94823 12901707776 "PUT /api/auth/profile/ HTTP/1.1" 200 260
ERROR 2025-05-22 04:52:52,787 log 94823 12952186880 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-22 04:52:52,788 basehttp 94823 12952186880 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 500 79
ERROR 2025-05-22 04:52:52,863 log 94823 12935360512 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-22 04:52:52,863 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 500 79
ERROR 2025-05-22 04:52:52,893 log 94823 12935360512 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-22 04:52:52,893 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 500 79
INFO 2025-05-22 04:52:56,896 basehttp 94823 12935360512 "GET /admin/user/user/ HTTP/1.1" 200 14657
INFO 2025-05-22 04:52:56,996 basehttp 94823 12935360512 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
INFO 2025-05-22 04:53:21,155 basehttp 94823 12901707776 "POST /api/auth/login/ HTTP/1.1" 200 303
WARNING 2025-05-22 04:53:38,144 log 94823 12901707776 Not Found: /api/auth/claim-temporary-invitation/
WARNING 2025-05-22 04:53:38,145 basehttp 94823 12901707776 "POST /api/auth/claim-temporary-invitation/ HTTP/1.1" 404 70
ERROR 2025-05-22 12:02:33,004 log 94823 12935360512 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-22 12:02:33,008 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 500 79
ERROR 2025-05-22 12:02:33,083 log 94823 12935360512 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-22 12:02:33,084 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 500 79
INFO 2025-05-22 12:02:51,784 basehttp 94823 12935360512 "GET /api/crypto/get_report/BTCUSDT/?language=zh-CN&_t=1747915356683 HTTP/1.1" 200 2159
ERROR 2025-05-22 12:02:52,578 log 94823 12935360512 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-22 12:02:52,579 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 500 79
ERROR 2025-05-22 12:02:57,430 log 94823 12935360512 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-22 12:02:57,430 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 500 79
ERROR 2025-05-22 12:02:58,264 log 94823 12935360512 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-22 12:02:58,264 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 500 79
ERROR 2025-05-22 12:02:58,848 log 94823 12935360512 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-22 12:02:58,849 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 500 79
ERROR 2025-05-22 12:02:59,269 log 94823 12935360512 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-22 12:02:59,269 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 500 79
ERROR 2025-05-22 12:02:59,520 log 94823 12935360512 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-22 12:02:59,521 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 500 79
ERROR 2025-05-22 12:02:59,716 log 94823 12935360512 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-22 12:02:59,716 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 500 79
INFO 2025-05-22 12:03:02,378 basehttp 94823 12935360512 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:03:02,393 basehttp 94823 12935360512 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
ERROR 2025-05-22 12:03:04,199 log 94823 12935360512 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-22 12:03:04,199 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 500 79
ERROR 2025-05-22 12:03:04,223 log 94823 12935360512 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-22 12:03:04,224 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 500 79
INFO 2025-05-22 12:03:09,888 basehttp 94823 12935360512 "GET /api/crypto/get_report/BTCUSDT/?language=zh-CN&_t=1747915389748 HTTP/1.1" 200 2159
ERROR 2025-05-22 12:03:23,601 log 94823 12935360512 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-22 12:03:23,602 basehttp 94823 12935360512 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 500 79
INFO 2025-05-22 12:05:18,993 autoreload 94823 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/CryptoAnalyst/views_technical_indicators.py changed, reloading.
INFO 2025-05-22 12:05:20,061 autoreload 28073 8462421120 Watching for file changes with StatReloader
INFO 2025-05-22 12:12:24,327 basehttp 28073 6204518400 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:12:24,387 basehttp 28073 6204518400 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:12:28,807 basehttp 28073 6204518400 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:12:28,829 basehttp 28073 6204518400 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:12:29,882 basehttp 28073 6204518400 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-22 12:12:29,947 basehttp 28073 6238171136 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:12:29,957 basehttp 28073 6204518400 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:12:29,974 basehttp 28073 6254997504 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:12:32,106 basehttp 28073 6204518400 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:12:32,107 basehttp 28073 6254997504 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:12:32,144 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:12:33,783 basehttp 28073 6204518400 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:12:33,840 basehttp 28073 6204518400 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:12:40,022 basehttp 28073 6204518400 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:12:40,089 basehttp 28073 6204518400 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:12:45,010 basehttp 28073 6204518400 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:12:45,030 basehttp 28073 6204518400 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:12:45,936 basehttp 28073 6238171136 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:12:45,943 basehttp 28073 6204518400 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:12:45,952 basehttp 28073 6254997504 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:12:49,283 basehttp 28073 6254997504 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:12:49,286 basehttp 28073 6204518400 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:12:49,311 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:12:51,043 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:12:51,100 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:14:53,917 basehttp 28073 6238171136 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:14:53,930 basehttp 28073 6204518400 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:14:53,954 basehttp 28073 6254997504 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:14:54,068 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:14:55,882 basehttp 28073 6238171136 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:14:55,885 basehttp 28073 6204518400 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:14:55,909 basehttp 28073 6254997504 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:14:55,939 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:14:57,184 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2891
INFO 2025-05-22 12:14:57,245 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2891
INFO 2025-05-22 12:15:08,906 basehttp 28073 6204518400 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2891
INFO 2025-05-22 12:15:08,989 basehttp 28073 6204518400 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2891
INFO 2025-05-22 12:15:28,580 basehttp 28073 6204518400 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:15:28,620 basehttp 28073 6238171136 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:15:28,634 basehttp 28073 6271823872 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:15:28,719 basehttp 28073 6254997504 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2891
INFO 2025-05-22 12:15:28,748 basehttp 28073 6238171136 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:15:28,791 basehttp 28073 6254997504 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2891
INFO 2025-05-22 12:15:28,967 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2891
INFO 2025-05-22 12:15:29,035 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2891
INFO 2025-05-22 12:15:32,220 basehttp 28073 6271823872 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:15:32,223 basehttp 28073 6238171136 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:15:32,248 basehttp 28073 6254997504 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:15:32,271 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:15:34,407 basehttp 28073 6204518400 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:15:34,413 basehttp 28073 6254997504 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:15:34,426 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:15:34,449 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:15:36,114 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:15:36,208 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:15:48,542 basehttp 28073 6238171136 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:15:48,545 basehttp 28073 6204518400 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:15:48,563 basehttp 28073 6254997504 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:15:48,590 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:15:48,609 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:15:52,024 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:15:52,194 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:15:59,144 basehttp 28073 6238171136 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:15:59,155 basehttp 28073 6204518400 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:15:59,169 basehttp 28073 6254997504 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:15:59,193 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:15:59,220 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:15:59,243 basehttp 28073 6238171136 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:16:03,370 basehttp 28073 6204518400 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:16:03,457 basehttp 28073 6204518400 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3333
INFO 2025-05-22 12:19:01,247 autoreload 28073 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/CryptoAnalyst/views_technical_indicators.py changed, reloading.
INFO 2025-05-22 12:19:02,492 autoreload 29551 8462421120 Watching for file changes with StatReloader
WARNING 2025-05-22 12:19:23,815 log 29551 6132887552 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-22 12:19:23,818 basehttp 29551 6132887552 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 404 132
WARNING 2025-05-22 12:19:23,889 log 29551 6132887552 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-22 12:19:23,890 basehttp 29551 6132887552 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 404 132
INFO 2025-05-22 12:19:26,732 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:19:26,751 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:19:28,469 basehttp 29551 6166540288 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:19:28,471 basehttp 29551 6132887552 "PUT /api/auth/profile/ HTTP/1.1" 200 260
WARNING 2025-05-22 12:19:28,496 log 29551 6183366656 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-22 12:19:28,497 basehttp 29551 6183366656 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 404 132
INFO 2025-05-22 12:19:30,645 basehttp 29551 6132887552 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:19:30,649 basehttp 29551 6183366656 "PUT /api/auth/profile/ HTTP/1.1" 200 260
WARNING 2025-05-22 12:19:30,671 log 29551 6166540288 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-22 12:19:30,672 basehttp 29551 6166540288 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 404 132
INFO 2025-05-22 12:19:32,642 basehttp 29551 6166540288 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:19:32,798 basehttp 29551 6166540288 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:19:35,092 basehttp 29551 6166540288 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:19:35,106 basehttp 29551 6132887552 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:19:35,123 basehttp 29551 6183366656 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:19:35,147 basehttp 29551 6166540288 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:19:37,309 basehttp 29551 6132887552 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:19:37,314 basehttp 29551 6183366656 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:19:37,327 basehttp 29551 6166540288 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:19:37,362 basehttp 29551 6166540288 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
WARNING 2025-05-22 12:19:39,306 log 29551 6132887552 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-22 12:19:39,307 basehttp 29551 6132887552 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 404 132
WARNING 2025-05-22 12:19:39,386 log 29551 6132887552 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-22 12:19:39,386 basehttp 29551 6132887552 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 404 132
INFO 2025-05-22 12:20:11,446 basehttp 29551 6132887552 "GET /api/crypto/get_report/BTCUSDT/?language=ja-JP&_t=1747916380360 HTTP/1.1" 200 3012
INFO 2025-05-22 12:20:12,297 basehttp 29551 6132887552 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3311
INFO 2025-05-22 12:20:13,347 basehttp 29551 6132887552 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3311
INFO 2025-05-22 12:20:24,782 basehttp 29551 6167113728 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:20:24,787 basehttp 29551 6132887552 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:20:24,815 basehttp 29551 6183940096 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3311
INFO 2025-05-22 12:20:24,841 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3311
INFO 2025-05-22 12:20:24,870 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3311
INFO 2025-05-22 12:20:26,701 basehttp 29551 6132887552 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:20:26,706 basehttp 29551 6183940096 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:20:26,720 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3311
INFO 2025-05-22 12:20:26,741 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3311
INFO 2025-05-22 12:20:26,768 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ja-JP HTTP/1.1" 200 3311
INFO 2025-05-22 12:20:43,915 basehttp 29551 6132887552 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2884
INFO 2025-05-22 12:20:44,036 basehttp 29551 6132887552 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2884
INFO 2025-05-22 12:20:58,759 basehttp 29551 6167113728 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:20:58,778 basehttp 29551 6132887552 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:20:58,795 basehttp 29551 6183940096 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2884
INFO 2025-05-22 12:20:58,865 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2884
INFO 2025-05-22 12:20:58,901 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2884
INFO 2025-05-22 12:20:58,940 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2884
INFO 2025-05-22 12:21:00,354 basehttp 29551 6132887552 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:21:00,359 basehttp 29551 6183940096 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:21:00,378 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2884
INFO 2025-05-22 12:21:00,420 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2884
INFO 2025-05-22 12:21:00,452 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2884
INFO 2025-05-22 12:21:00,481 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2884
INFO 2025-05-22 12:21:02,174 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:21:02,264 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:21:33,393 basehttp 29551 6132887552 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:21:33,481 basehttp 29551 6167113728 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:21:33,517 basehttp 29551 6183940096 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:21:33,616 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:21:33,678 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:21:33,732 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:21:33,782 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:21:34,742 basehttp 29551 6132887552 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:21:34,746 basehttp 29551 6183940096 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:21:34,764 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:21:34,812 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:21:34,838 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:21:34,852 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
INFO 2025-05-22 12:21:34,870 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 200 2458
WARNING 2025-05-22 12:21:36,298 log 29551 6167113728 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-22 12:21:36,298 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 404 132
WARNING 2025-05-22 12:21:36,362 log 29551 6167113728 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-22 12:21:36,363 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 404 132
INFO 2025-05-22 12:21:49,842 basehttp 29551 6167113728 "GET /api/crypto/get_report/BTCUSDT/?language=ko-KR&_t=1747916497247 HTTP/1.1" 200 2639
INFO 2025-05-22 12:21:51,164 basehttp 29551 6132887552 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 2938
INFO 2025-05-22 12:21:52,292 basehttp 29551 6132887552 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 2938
INFO 2025-05-22 12:22:19,570 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:22:19,589 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:22:21,355 basehttp 29551 6167113728 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:22:21,361 basehttp 29551 6132887552 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:22:21,552 basehttp 29551 6183940096 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 2938
INFO 2025-05-22 12:22:21,647 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 2938
INFO 2025-05-22 12:22:21,711 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 2938
INFO 2025-05-22 12:22:21,752 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 2938
INFO 2025-05-22 12:22:21,791 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 2938
INFO 2025-05-22 12:22:21,821 basehttp 29551 6167113728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 2938
INFO 2025-05-22 12:22:24,328 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:22:24,355 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:23:40,387 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:23:40,441 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:24:31,853 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:24:32,344 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:24:42,354 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:24:42,407 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:24:54,699 basehttp 29551 6167113728 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:24:54,713 basehttp 29551 6132887552 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:24:56,088 basehttp 29551 6167113728 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:24:56,153 basehttp 29551 6167113728 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:26:24,951 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:26:24,978 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:26:29,236 basehttp 29551 6167113728 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:26:29,245 basehttp 29551 6132887552 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:26:30,456 basehttp 29551 6167113728 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:26:30,481 basehttp 29551 6167113728 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:27:07,761 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:27:07,781 basehttp 29551 6167113728 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:36:11,405 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:36:11,425 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:36:13,604 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:36:13,636 basehttp 29551 6132887552 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:38:18,943 autoreload 29551 8462421120 /Users/<USER>/Documents/Projects/Cooltrade/backend/config/settings.py changed, reloading.
INFO 2025-05-22 12:38:19,689 autoreload 32789 8462421120 Watching for file changes with StatReloader
INFO 2025-05-22 12:38:29,081 autoreload 33369 8462421120 Watching for file changes with StatReloader
INFO 2025-05-22 12:39:31,514 basehttp 32789 6200406016 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:39:31,538 basehttp 32789 6217232384 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:39:31,544 basehttp 32789 6200406016 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:39:31,564 basehttp 32789 6200406016 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:39:39,179 basehttp 32789 6200406016 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:39:39,203 basehttp 32789 6217232384 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:39:39,211 basehttp 32789 6200406016 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:39:39,236 basehttp 32789 6200406016 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:39:40,809 basehttp 32789 6200406016 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:39:40,856 basehttp 32789 6200406016 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:40:27,276 basehttp 32789 6200406016 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:40:27,316 basehttp 32789 6200406016 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:43:38,012 basehttp 32789 6200406016 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:43:38,047 basehttp 32789 6200406016 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:43:39,500 basehttp 32789 6217232384 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:43:39,511 basehttp 32789 6200406016 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:43:40,994 basehttp 32789 6217232384 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:43:41,030 basehttp 32789 6217232384 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:43:42,697 basehttp 32789 6217232384 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:43:42,698 basehttp 32789 6200406016 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:43:57,928 basehttp 32789 6200406016 "POST /api/auth/login/ HTTP/1.1" 200 295
INFO 2025-05-22 12:43:58,453 basehttp 32789 6200406016 "OPTIONS /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 0
INFO 2025-05-22 12:43:58,454 basehttp 32789 6217232384 "OPTIONS /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 0
INFO 2025-05-22 12:43:58,511 basehttp 32789 6217232384 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 2938
INFO 2025-05-22 12:43:58,551 basehttp 32789 6217232384 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 2938
INFO 2025-05-22 12:44:01,135 basehttp 32789 6250885120 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 12:44:01,160 basehttp 32789 6250885120 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 12:44:01,920 basehttp 32789 6250885120 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:44:01,923 basehttp 32789 6217232384 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:44:01,929 basehttp 32789 6200406016 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 2938
INFO 2025-05-22 12:44:55,357 basehttp 32789 6250885120 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:44:55,367 basehttp 32789 6200406016 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:44:55,367 basehttp 32789 6200406016 - Broken pipe from ('************', 62389)
INFO 2025-05-22 12:44:55,382 basehttp 32789 6217232384 "GET /api/crypto/technical-indicators/BTCUSDT/?language=ko-KR HTTP/1.1" 200 2938
INFO 2025-05-22 12:44:55,385 basehttp 32789 6217232384 - Broken pipe from ('************', 62390)
INFO 2025-05-22 12:44:55,745 basehttp 32789 6217232384 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:44:55,815 basehttp 32789 6200406016 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:45:08,247 basehttp 32789 6200406016 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:45:08,247 basehttp 32789 6200406016 - Broken pipe from ('************', 62629)
INFO 2025-05-22 12:45:08,253 basehttp 32789 6217232384 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:45:09,398 basehttp 32789 6200406016 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:45:09,399 basehttp 32789 6217232384 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:46:41,553 basehttp 32789 6217232384 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:46:41,674 basehttp 32789 6200406016 - Broken pipe from ('************', 63702)
INFO 2025-05-22 12:46:41,802 basehttp 32789 6217232384 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:46:41,807 basehttp 32789 6200406016 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:47:03,764 basehttp 32789 6200406016 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:47:03,752 basehttp 32789 6217232384 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:47:03,766 basehttp 32789 6200406016 - Broken pipe from ('************', 63974)
INFO 2025-05-22 12:47:04,729 basehttp 32789 6217232384 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:47:04,733 basehttp 32789 6200406016 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:48:47,674 basehttp 32789 6217232384 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:48:47,691 basehttp 32789 6200406016 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:49:07,478 basehttp 32789 6217232384 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:49:07,483 basehttp 32789 6200406016 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:50:43,367 basehttp 32789 6200406016 - Broken pipe from ('************', 49895)
INFO 2025-05-22 12:50:43,372 basehttp 32789 6217232384 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:50:43,963 basehttp 32789 6217232384 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:50:43,985 basehttp 32789 6200406016 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:50:48,262 basehttp 32789 6217232384 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:50:48,269 basehttp 32789 6200406016 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 12:50:48,269 basehttp 32789 6200406016 - Broken pipe from ('************', 49930)
INFO 2025-05-22 12:50:48,861 basehttp 32789 6217232384 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 12:50:48,866 basehttp 32789 6200406016 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 23:28:39,476 autoreload 98293 8462421120 Watching for file changes with StatReloader
INFO 2025-05-22 23:28:43,397 basehttp 98293 6204600320 "POST /api/auth/login/ HTTP/1.1" 200 295
INFO 2025-05-22 23:31:22,935 basehttp 98293 6204600320 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 23:31:22,976 basehttp 98293 6204600320 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 23:31:25,296 basehttp 98293 6204600320 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-22 23:31:25,318 basehttp 98293 6221426688 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 23:31:25,330 basehttp 98293 6204600320 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-22 23:31:30,002 basehttp 98293 6221426688 "GET /api/auth/invitation-info/ HTTP/1.1" 200 281
INFO 2025-05-22 23:31:30,017 basehttp 98293 6221426688 "GET /api/auth/invitation-info/ranking/ HTTP/1.1" 200 32
INFO 2025-05-22 23:31:43,296 basehttp 98293 6221426688 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-22 23:31:43,297 basehttp 98293 6204600320 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-23 04:34:11,177 autoreload 17725 8300977920 Watching for file changes with StatReloader
INFO 2025-05-23 04:34:34,181 basehttp 17725 6199177216 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-23 04:34:34,246 basehttp 17725 6216003584 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-23 04:34:34,254 basehttp 17725 6199177216 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-23 04:34:43,277 basehttp 17725 6216003584 "POST /api/auth/login/ HTTP/1.1" 200 295
INFO 2025-05-23 04:38:19,150 basehttp 17725 6216003584 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-23 04:38:19,151 basehttp 17725 6199177216 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-23 04:38:19,371 basehttp 17725 6199177216 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-23 04:38:19,371 basehttp 17725 6216003584 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-23 04:38:20,016 basehttp 17725 6216003584 "GET /api/auth/profile/ HTTP/1.1" 200 235
INFO 2025-05-23 04:38:20,026 basehttp 17725 6199177216 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-23 04:44:08,803 basehttp 17725 6216003584 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-05-23 04:44:08,845 basehttp 17725 6216003584 "PUT /api/auth/profile/ HTTP/1.1" 200 260
INFO 2025-05-23 10:41:58,482 autoreload 17725 8300977920 /Users/<USER>/Documents/Projects/Cooltrade/backend/CryptoAnalyst/views_report.py changed, reloading.
INFO 2025-05-23 10:42:00,288 autoreload 32639 8300977920 Watching for file changes with StatReloader
INFO 2025-05-23 10:42:10,854 autoreload 32639 8300977920 /Users/<USER>/Documents/Projects/Cooltrade/backend/CryptoAnalyst/views_report.py changed, reloading.
INFO 2025-05-23 10:42:14,333 autoreload 32658 8300977920 Watching for file changes with StatReloader
INFO 2025-05-23 10:42:23,636 autoreload 32658 8300977920 /Users/<USER>/Documents/Projects/Cooltrade/backend/CryptoAnalyst/views_report.py changed, reloading.
INFO 2025-05-23 10:42:24,609 autoreload 32673 8300977920 Watching for file changes with StatReloader
INFO 2025-05-23 10:42:34,775 autoreload 32673 8300977920 /Users/<USER>/Documents/Projects/Cooltrade/backend/CryptoAnalyst/views_report.py changed, reloading.
INFO 2025-05-23 10:42:35,469 autoreload 32686 8300977920 Watching for file changes with StatReloader
INFO 2025-05-23 13:37:12,504 autoreload 32686 8300977920 /Users/<USER>/Documents/Projects/Cooltrade/backend/config/celery.py changed, reloading.
INFO 2025-05-23 13:37:13,011 autoreload 36969 8300977920 Watching for file changes with StatReloader
INFO 2025-05-23 13:37:39,332 autoreload 36969 8300977920 /Users/<USER>/Documents/Projects/Cooltrade/backend/config/celery.py changed, reloading.
INFO 2025-05-23 13:37:39,641 autoreload 37588 8300977920 Watching for file changes with StatReloader
INFO 2025-05-23 14:12:38,013 basehttp 37588 6161952768 "GET / HTTP/1.1" 200 55142
INFO 2025-05-23 14:12:38,059 basehttp 37588 6161952768 "GET /static/website/images/icon128.png HTTP/1.1" 304 0
INFO 2025-05-23 14:12:38,061 basehttp 37588 6325039104 "GET /static/website/images/binance.ico HTTP/1.1" 304 0
INFO 2025-05-23 14:12:38,065 basehttp 37588 6325039104 "GET /static/website/images/okx.ico HTTP/1.1" 304 0
INFO 2025-05-23 14:12:38,068 basehttp 37588 6161952768 "GET /static/website/images/gate.ico HTTP/1.1" 304 0
INFO 2025-05-23 14:12:38,099 basehttp 37588 6161952768 "GET /static/website/images/bybit.ico HTTP/1.1" 304 0
INFO 2025-05-23 14:12:38,768 basehttp 37588 6161952768 "GET /static/website/images/htx.ico HTTP/1.1" 304 0
WARNING 2025-05-23 14:12:38,781 basehttp 37588 6161952768 "GET /static/website/images/btc.png HTTP/1.1" 404 1929
WARNING 2025-05-23 14:12:39,124 basehttp 37588 6325039104 "GET /static/website/images/hero-bg.jpg HTTP/1.1" 404 1941
INFO 2025-05-23 14:12:40,679 basehttp 37588 6161952768 "GET /static/website/images/favicon.ico HTTP/1.1" 304 0
INFO 2025-05-23 14:17:07,864 basehttp 37588 6161952768 "GET / HTTP/1.1" 200 54634
WARNING 2025-05-23 14:17:07,911 basehttp 37588 6161952768 "GET /static/website/images/btc.png HTTP/1.1" 404 1929
WARNING 2025-05-23 14:17:07,921 basehttp 37588 6325039104 "GET /static/website/images/logo.svg HTTP/1.1" 404 1932
WARNING 2025-05-23 14:17:07,989 basehttp 37588 6161952768 "GET /static/website/images/hero-bg.jpg HTTP/1.1" 404 1941
INFO 2025-05-23 14:20:19,653 basehttp 37588 6161952768 "GET / HTTP/1.1" 200 42377
WARNING 2025-05-23 14:20:19,717 basehttp 37588 6325039104 "GET /static/website/images/logo.svg HTTP/1.1" 404 1932
WARNING 2025-05-23 14:20:19,717 basehttp 37588 6161952768 "GET /static/website/images/btc.png HTTP/1.1" 404 1929
WARNING 2025-05-23 14:20:19,769 basehttp 37588 6161952768 "GET /static/website/images/hero-bg.jpg HTTP/1.1" 404 1941
INFO 2025-05-23 14:25:32,970 basehttp 37588 6161952768 "GET / HTTP/1.1" 200 42407
WARNING 2025-05-23 14:25:33,109 basehttp 37588 6161952768 "GET /static/website/images/btc.png HTTP/1.1" 404 1929
WARNING 2025-05-23 14:25:33,192 basehttp 37588 6325039104 "GET /static/website/images/hero-bg.jpg HTTP/1.1" 404 1941
INFO 2025-05-23 14:27:21,982 basehttp 37588 6161952768 "GET / HTTP/1.1" 200 42436
WARNING 2025-05-23 14:27:22,100 basehttp 37588 6161952768 "GET /static/website/images/btc.png HTTP/1.1" 404 1929
WARNING 2025-05-23 14:27:22,156 basehttp 37588 6325039104 "GET /static/website/images/hero-bg.jpg HTTP/1.1" 404 1941
INFO 2025-05-23 14:35:07,778 basehttp 37588 6161952768 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-05-23 14:35:07,804 basehttp 37588 6161952768 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4188
INFO 2025-05-23 14:35:07,852 basehttp 37588 6161952768 "GET /static/admin/css/base.css HTTP/1.1" 304 0
INFO 2025-05-23 14:35:07,867 basehttp 37588 6358691840 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
INFO 2025-05-23 14:35:07,886 basehttp 37588 6325039104 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
INFO 2025-05-23 14:35:07,884 basehttp 37588 6375518208 "GET /static/admin/css/login.css HTTP/1.1" 304 0
INFO 2025-05-23 14:35:07,884 basehttp 37588 6341865472 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
INFO 2025-05-23 14:35:08,175 basehttp 37588 6341865472 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
INFO 2025-05-23 14:35:08,176 basehttp 37588 6325039104 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
WARNING 2025-05-23 14:35:08,322 log 37588 6325039104 Not Found: /favicon.ico
WARNING 2025-05-23 14:35:08,323 basehttp 37588 6325039104 "GET /favicon.ico HTTP/1.1" 404 2931
INFO 2025-05-23 14:35:11,736 basehttp 37588 6161952768 "GET / HTTP/1.1" 200 42436
INFO 2025-05-23 14:35:12,107 basehttp 37588 6161952768 "GET /static/website/images/binance.ico HTTP/1.1" 304 0
INFO 2025-05-23 14:35:12,111 basehttp 37588 6161952768 "GET /static/website/images/okx.ico HTTP/1.1" 304 0
INFO 2025-05-23 14:35:12,117 basehttp 37588 6161952768 "GET /static/website/images/gate.ico HTTP/1.1" 304 0
INFO 2025-05-23 14:35:12,124 basehttp 37588 6161952768 "GET /static/website/images/bybit.ico HTTP/1.1" 304 0
INFO 2025-05-23 14:35:12,164 basehttp 37588 6161952768 "GET /static/website/images/htx.ico HTTP/1.1" 304 0
WARNING 2025-05-23 14:35:12,169 basehttp 37588 6161952768 "GET /static/website/images/btc.png HTTP/1.1" 404 1929
WARNING 2025-05-23 14:35:12,235 basehttp 37588 6325039104 "GET /static/website/images/hero-bg.jpg HTTP/1.1" 404 1941
INFO 2025-05-23 14:35:12,264 basehttp 37588 6161952768 "GET /static/website/images/favicon.ico HTTP/1.1" 304 0
INFO 2025-05-23 14:43:08,954 basehttp 37588 6161952768 "GET / HTTP/1.1" 200 42436
WARNING 2025-05-23 14:43:09,356 basehttp 37588 6161952768 "GET /static/website/images/btc.png HTTP/1.1" 404 1929
WARNING 2025-05-23 14:43:09,812 basehttp 37588 6325039104 "GET /static/website/images/hero-bg.jpg HTTP/1.1" 404 1941
INFO 2025-05-23 14:44:16,918 basehttp 37588 6161952768 "GET / HTTP/1.1" 200 42436
WARNING 2025-05-23 14:44:17,167 basehttp 37588 6161952768 "GET /static/website/images/btc.png HTTP/1.1" 404 1929
WARNING 2025-05-23 14:44:17,223 basehttp 37588 6325039104 "GET /static/website/images/hero-bg.jpg HTTP/1.1" 404 1941
INFO 2025-05-23 14:44:26,319 basehttp 37588 6161952768 "GET /static/website/images/favicon.ico HTTP/1.1" 200 4286
INFO 2025-05-23 14:44:29,709 basehttp 37588 6161952768 "GET / HTTP/1.1" 200 42436
WARNING 2025-05-23 14:44:29,866 basehttp 37588 6161952768 "GET /static/website/images/btc.png HTTP/1.1" 404 1929
WARNING 2025-05-23 14:44:29,913 basehttp 37588 6325039104 "GET /static/website/images/hero-bg.jpg HTTP/1.1" 404 1941
INFO 2025-05-23 14:44:51,688 basehttp 37588 6161952768 "GET / HTTP/1.1" 200 42436
INFO 2025-05-23 14:44:52,587 basehttp 37588 6161952768 "GET /static/website/images/binance.ico HTTP/1.1" 200 4286
INFO 2025-05-23 14:44:52,591 basehttp 37588 6161952768 "GET /static/website/images/okx.ico HTTP/1.1" 200 2462
INFO 2025-05-23 14:44:52,595 basehttp 37588 6161952768 "GET /static/website/images/gate.ico HTTP/1.1" 200 619
INFO 2025-05-23 14:44:52,601 basehttp 37588 6161952768 "GET /static/website/images/bybit.ico HTTP/1.1" 200 15086
INFO 2025-05-23 14:44:52,676 basehttp 37588 6161952768 "GET /static/website/images/htx.ico HTTP/1.1" 200 1536
WARNING 2025-05-23 14:44:52,685 basehttp 37588 6161952768 "GET /static/website/images/btc.png HTTP/1.1" 404 1929
WARNING 2025-05-23 14:44:52,743 basehttp 37588 6325039104 "GET /static/website/images/hero-bg.jpg HTTP/1.1" 404 1941
INFO 2025-05-23 14:44:53,181 basehttp 37588 6161952768 "GET /static/website/images/favicon.ico HTTP/1.1" 200 4286
INFO 2025-05-23 14:44:54,506 basehttp 37588 6161952768 "GET / HTTP/1.1" 200 42436
INFO 2025-05-23 14:44:54,684 basehttp 37588 6161952768 "GET / HTTP/1.1" 200 42436
INFO 2025-05-23 14:44:54,946 basehttp 37588 6161952768 "GET /static/website/images/binance.ico HTTP/1.1" 200 4286
INFO 2025-05-23 14:44:54,948 basehttp 37588 6161952768 "GET /static/website/images/okx.ico HTTP/1.1" 200 2462
INFO 2025-05-23 14:44:54,950 basehttp 37588 6161952768 "GET /static/website/images/gate.ico HTTP/1.1" 200 619
INFO 2025-05-23 14:44:54,953 basehttp 37588 6161952768 "GET /static/website/images/bybit.ico HTTP/1.1" 200 15086
INFO 2025-05-23 14:44:55,029 basehttp 37588 6161952768 "GET / HTTP/1.1" 200 42436
INFO 2025-05-23 14:44:55,187 basehttp 37588 6161952768 "GET /static/website/images/binance.ico HTTP/1.1" 200 4286
INFO 2025-05-23 14:44:55,189 basehttp 37588 6161952768 "GET /static/website/images/okx.ico HTTP/1.1" 200 2462
INFO 2025-05-23 14:44:55,191 basehttp 37588 6161952768 "GET /static/website/images/gate.ico HTTP/1.1" 200 619
INFO 2025-05-23 14:44:55,193 basehttp 37588 6161952768 "GET /static/website/images/bybit.ico HTTP/1.1" 200 15086
INFO 2025-05-23 14:44:55,237 basehttp 37588 6161952768 "GET / HTTP/1.1" 200 42436
INFO 2025-05-23 14:44:55,583 basehttp 37588 6161952768 "GET /static/website/images/binance.ico HTTP/1.1" 200 4286
INFO 2025-05-23 14:44:55,586 basehttp 37588 6161952768 "GET /static/website/images/okx.ico HTTP/1.1" 200 2462
INFO 2025-05-23 14:44:55,588 basehttp 37588 6161952768 "GET /static/website/images/gate.ico HTTP/1.1" 200 619
INFO 2025-05-23 14:44:55,591 basehttp 37588 6161952768 "GET /static/website/images/bybit.ico HTTP/1.1" 200 15086
INFO 2025-05-23 14:44:55,618 basehttp 37588 6161952768 "GET /static/website/images/htx.ico HTTP/1.1" 200 1536
WARNING 2025-05-23 14:44:55,623 basehttp 37588 6325039104 "GET /static/website/images/btc.png HTTP/1.1" 404 1929
WARNING 2025-05-23 14:44:55,687 basehttp 37588 6161952768 "GET /static/website/images/hero-bg.jpg HTTP/1.1" 404 1941
INFO 2025-05-23 14:44:56,007 basehttp 37588 6161952768 "GET /static/website/images/favicon.ico HTTP/1.1" 200 4286
INFO 2025-05-23 14:44:56,581 basehttp 37588 6161952768 "GET /static/website/images/favicon.ico HTTP/1.1" 200 4286
WARNING 2025-05-24 01:27:31,263 log 37588 6161952768 Not Found: /favicon.ico
WARNING 2025-05-24 01:27:31,265 basehttp 37588 6161952768 "GET /favicon.ico HTTP/1.1" 404 2931
INFO 2025-05-24 01:27:45,399 basehttp 37588 6161952768 "GET /admin/user/user/ HTTP/1.1" 200 14654
INFO 2025-05-24 01:27:45,464 basehttp 37588 6161952768 "GET /admin/jsi18n/ HTTP/1.1" 200 8560
WARNING 2025-05-24 01:27:45,707 log 37588 6161952768 Not Found: /favicon.ico
WARNING 2025-05-24 01:27:45,707 basehttp 37588 6161952768 "GET /favicon.ico HTTP/1.1" 404 2931
INFO 2025-05-24 01:32:48,870 autoreload 37588 8300977920 /Users/<USER>/Documents/Projects/Cooltrade/backend/CryptoAnalyst/views_technical_indicators.py changed, reloading.
INFO 2025-05-24 01:32:50,046 autoreload 72225 8300977920 Watching for file changes with StatReloader
INFO 2025-05-24 01:32:57,094 autoreload 72225 8300977920 /Users/<USER>/Documents/Projects/Cooltrade/backend/CryptoAnalyst/views_technical_indicators.py changed, reloading.
INFO 2025-05-24 01:33:01,248 autoreload 72245 8300977920 Watching for file changes with StatReloader
INFO 2025-05-24 01:33:16,277 autoreload 72245 8300977920 /Users/<USER>/Documents/Projects/Cooltrade/backend/CryptoAnalyst/services/gate_api.py changed, reloading.
INFO 2025-05-24 01:33:17,943 autoreload 72274 8300977920 Watching for file changes with StatReloader
INFO 2025-05-24 01:33:39,201 autoreload 72274 8300977920 /Users/<USER>/Documents/Projects/Cooltrade/backend/CryptoAnalyst/services/technical_analysis.py changed, reloading.
INFO 2025-05-24 01:33:40,521 autoreload 72303 8300977920 Watching for file changes with StatReloader
INFO 2025-05-24 01:34:11,129 autoreload 72303 8300977920 /Users/<USER>/Documents/Projects/Cooltrade/backend/CryptoAnalyst/views_technical_indicators.py changed, reloading.
INFO 2025-05-24 01:34:13,016 autoreload 72342 8300977920 Watching for file changes with StatReloader
INFO 2025-05-24 01:37:57,253 autoreload 72506 8300977920 Watching for file changes with StatReloader
INFO 2025-05-24 01:38:11,454 autoreload 72598 8300977920 Watching for file changes with StatReloader
WARNING 2025-05-24 01:38:21,452 log 72598 6208106496 Bad Request: /api/auth/login/
WARNING 2025-05-24 01:38:21,453 basehttp 72598 6208106496 "POST /api/auth/login/ HTTP/1.1" 400 52
INFO 2025-05-24 01:38:30,515 basehttp 72598 6208106496 "POST /api/auth/login/ HTTP/1.1" 200 295
INFO 2025-05-24 01:38:51,194 basehttp 72598 6208106496 "GET /api/crypto/technical-indicators-data/BTCUSDT/ HTTP/1.1" 200 413
INFO 2025-05-24 01:39:16,009 basehttp 72598 6208106496 "GET /api/crypto/get_report/BTCUSDT/?language=en-US HTTP/1.1" 200 2739
WARNING 2025-05-24 01:39:32,214 log 72598 6208106496 Unauthorized: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-24 01:39:32,216 basehttp 72598 6208106496 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 401 34
ERROR 2025-05-24 01:39:37,324 log 72598 6208106496 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-24 01:39:37,324 basehttp 72598 6208106496 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 500 82
WARNING 2025-05-24 01:40:04,320 log 72598 6242332672 Unauthorized: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-24 01:40:04,320 basehttp 72598 6242332672 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 401 5348
INFO 2025-05-24 01:40:04,525 basehttp 72598 6242332672 "GET /static/rest_framework/css/bootstrap-tweaks.css HTTP/1.1" 304 0
INFO 2025-05-24 01:40:04,527 basehttp 72598 6242332672 "GET /static/rest_framework/js/bootstrap.min.js HTTP/1.1" 304 0
INFO 2025-05-24 01:40:04,528 basehttp 72598 6292811776 "GET /static/rest_framework/css/prettify.css HTTP/1.1" 304 0
INFO 2025-05-24 01:40:04,528 basehttp 72598 6275985408 "GET /static/rest_framework/css/default.css HTTP/1.1" 304 0
INFO 2025-05-24 01:40:04,528 basehttp 72598 6259159040 "GET /static/rest_framework/css/bootstrap.min.css HTTP/1.1" 304 0
INFO 2025-05-24 01:40:04,531 basehttp 72598 6292811776 "GET /static/rest_framework/js/jquery-3.5.1.min.js HTTP/1.1" 304 0
INFO 2025-05-24 01:40:04,537 basehttp 72598 6309638144 "GET /static/rest_framework/js/ajax-form.js HTTP/1.1" 304 0
INFO 2025-05-24 01:40:04,541 basehttp 72598 6326464512 "GET /static/rest_framework/js/csrf.js HTTP/1.1" 304 0
INFO 2025-05-24 01:40:04,543 basehttp 72598 6275985408 "GET /static/rest_framework/js/prettify-min.js HTTP/1.1" 304 0
INFO 2025-05-24 01:40:04,559 basehttp 72598 6259159040 "GET /static/rest_framework/js/default.js HTTP/1.1" 304 0
INFO 2025-05-24 01:40:44,536 autoreload 72598 8300977920 /Users/<USER>/Documents/Projects/Cooltrade/backend/CryptoAnalyst/views_technical_indicators.py changed, reloading.
INFO 2025-05-24 01:40:49,269 autoreload 72810 8300977920 Watching for file changes with StatReloader
ERROR 2025-05-24 01:41:16,290 log 72810 6199341056 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-24 01:41:16,291 basehttp 72810 6199341056 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 500 98
INFO 2025-05-24 01:42:12,975 autoreload 72810 8300977920 /Users/<USER>/Documents/Projects/Cooltrade/backend/CryptoAnalyst/views_technical_indicators.py changed, reloading.
INFO 2025-05-24 01:42:15,272 autoreload 73584 8300977920 Watching for file changes with StatReloader
ERROR 2025-05-24 01:42:41,569 log 73584 6170324992 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-24 01:42:41,574 basehttp 73584 6170324992 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 500 98
ERROR 2025-05-24 01:42:45,336 log 73584 6170324992 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-24 01:42:45,336 basehttp 73584 6170324992 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 500 98
ERROR 2025-05-24 01:55:21,485 log 73584 6203977728 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-24 01:55:21,486 basehttp 73584 6203977728 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 500 98
INFO 2025-05-24 01:58:13,393 autoreload 73584 8300977920 /Users/<USER>/Documents/Projects/Cooltrade/backend/CryptoAnalyst/services/gate_api.py changed, reloading.
INFO 2025-05-24 01:58:14,373 autoreload 96844 8300977920 Watching for file changes with StatReloader
INFO 2025-05-24 01:59:15,920 autoreload 96844 8300977920 /Users/<USER>/Documents/Projects/Cooltrade/backend/CryptoAnalyst/services/technical_analysis.py changed, reloading.
INFO 2025-05-24 01:59:16,361 autoreload 96910 8300977920 Watching for file changes with StatReloader
ERROR 2025-05-24 01:59:23,796 log 96910 6135934976 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-24 01:59:23,797 basehttp 96910 6135934976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 500 98
ERROR 2025-05-24 01:59:38,529 log 96910 6135934976 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-24 01:59:38,529 basehttp 96910 6135934976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 500 98
ERROR 2025-05-24 01:59:54,543 log 96910 6135934976 Internal Server Error: /api/crypto/technical-indicators/BTCUSDT/
ERROR 2025-05-24 01:59:54,544 basehttp 96910 6135934976 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 500 98
INFO 2025-05-24 02:00:30,116 autoreload 96910 8300977920 /Users/<USER>/Documents/Projects/Cooltrade/backend/CryptoAnalyst/views_technical_indicators.py changed, reloading.
INFO 2025-05-24 02:00:30,597 autoreload 97218 8300977920 Watching for file changes with StatReloader
INFO 2025-05-24 02:00:38,304 basehttp 97218 6169407488 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2928
WARNING 2025-05-24 02:00:45,061 log 97218 6169407488 Not Found: /api/crypto/technical-indicators/BTCUSDT/
WARNING 2025-05-24 02:00:45,061 basehttp 97218 6169407488 "GET /api/crypto/technical-indicators/BTCUSDT/?language=zh-CN HTTP/1.1" 404 132
INFO 2025-05-24 03:18:44,185 basehttp 97218 6169407488 "GET /api/crypto/technical-indicators/BTCUSDT/?language=en-US HTTP/1.1" 200 2928
INFO 2025-05-24 03:27:26,074 autoreload 97218 8300977920 /Users/<USER>/Documents/Projects/Cooltrade/backend/CryptoAnalyst/views_indicators_data.py changed, reloading.
INFO 2025-05-24 03:27:26,678 autoreload 1427 8300977920 Watching for file changes with StatReloader
INFO 2025-05-24 03:27:35,458 basehttp 1427 6203011072 "GET /api/crypto/technical-indicators-data/BTCUSDT/ HTTP/1.1" 200 410
INFO 2025-05-24 03:27:43,607 basehttp 1427 6203011072 "GET /api/crypto/technical-indicators-data/ETHUSDT/ HTTP/1.1" 200 402
INFO 2025-05-24 03:27:49,252 basehttp 1427 6203011072 "GET /api/crypto/technical-indicators/ETHUSDT/?language=en-US HTTP/1.1" 200 2530
INFO 2025-05-24 03:34:28,054 autoreload 1873 8300977920 Watching for file changes with StatReloader
INFO 2025-05-26 07:10:02,341 autoreload 99133 8300977920 Watching for file changes with StatReloader
