{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cooltrade - AI Trading Assistant</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script src="https://unpkg.com/gsap@3.12.5/dist/gsap.min.js"></script>
    <script src="https://unpkg.com/gsap@3.12.5/dist/ScrollTrigger.min.js"></script>
    <script>tailwind.config={theme:{extend:{colors:{primary:'#5B67EA',secondary:'#4B56D9',dark:{900:'#0F1012',800:'#1A1B1E',700:'#1E1F23'}},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link rel="icon" href="{% static 'website/images/favicon.ico' %}">
    <style>
    :where([class^="ri-"])::before { content: "\f3c2"; }
    body {
        font-family: 'Space Grotesk', sans-serif;
        color: #fff;
        background-color: #0F1012;
        overflow-x: hidden;
    }
    .gradient-text {
        background: linear-gradient(90deg, #5B67EA, #7A85FF);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    .hero-bg {
        background: linear-gradient(135deg, #1A1B1E 0%, #0F1012 100%);
        position: relative;
        overflow: hidden;
    }
    .hero-bg::before {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background-image: url('{% static "website/images/hero-bg.jpg" %}');
        background-size: cover;
        background-position: center;
        opacity: 0.15;
        filter: blur(3px);
    }
    .hero-bg::after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle at center, rgba(91,103,234,0.2) 0%, rgba(15,16,18,0.95) 70%);
    }
    .glass-card {
        background: rgba(26, 27, 30, 0.5);
        backdrop-filter: blur(12px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
    }
    .animated-gradient {
        background: linear-gradient(90deg, #5B67EA, #7A85FF, #5B67EA);
        background-size: 200% 100%;
        animation: gradient 6s linear infinite;
    }
    @keyframes gradient {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }
    .hover-scale {
        transition: all 0.3s ease;
    }
    .hover-scale:hover {
        transform: scale(1.05);
    }
    .hover-lift {
        transition: all 0.3s ease;
    }
    .hover-lift:hover {
        transform: translateY(-8px);
    }
    .glow-effect {
        position: relative;
    }
    .glow-effect::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, #5B67EA, #7A85FF);
        border-radius: inherit;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    .glow-effect:hover::after {
        opacity: 0.5;
    }
    .mobile-menu {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }
    .mobile-menu.active {
        transform: translateX(0);
    }
    .scroll-indicator {
        animation: bounce 2s infinite;
    }
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }
    </style>
</head>
<body class="bg-dark-900">
    <!-- Mobile Menu -->
    <div id="mobileMenu" class="mobile-menu fixed top-0 right-0 w-full h-full bg-dark-800/95 backdrop-blur-xl z-50 flex flex-col justify-start pt-24 px-8">
        <button onclick="toggleMobileMenu()" class="absolute top-6 right-6 text-2xl text-gray-400 hover:text-white">
            <i class="ri-close-line"></i>
        </button>
        <div class="flex flex-col space-y-8 text-lg">
            <a href="#features" onclick="toggleMobileMenu()" class="text-gray-300 hover:text-white transition-colors">Features</a>
            <a href="#how-it-works" onclick="toggleMobileMenu()" class="text-gray-300 hover:text-white transition-colors">How It Works</a>
            <a href="#stats" onclick="toggleMobileMenu()" class="text-gray-300 hover:text-white transition-colors">Data Analysis</a>
            <a href="#" onclick="toggleMobileMenu()" class="text-gray-300 hover:text-white transition-colors">Blog</a>
        </div>
        <div class="mt-12">
            <button class="w-full bg-primary text-white px-6 py-3 rounded-button hover:bg-opacity-90 transition-all flex items-center justify-center hover-scale glow-effect">
                    <i class="ri-chrome-fill mr-2"></i>
                    Install Free Plugin
                </button>
        </div>
    </div>

    <!-- Navigation Bar -->
    <nav class="glass-card fixed top-0 left-0 right-0 py-4 px-6 md:px-12 flex items-center justify-between z-40">
        <div class="flex items-center">
            <img src="https://cooltrade.xyz/static/images/logo.png" alt="Cooltrade" class="h-10 w-10 rounded-full mr-2 inline-block align-middle hover-scale" />
            <span class="text-2xl font-bold text-white align-middle">Cooltrade</span>
        </div>
        <div class="hidden md:flex items-center space-x-8">
            <a href="#features" class="text-gray-300 hover:text-white transition-colors">Features</a>
            <a href="#how-it-works" class="text-gray-300 hover:text-white transition-colors">How It Works</a>
            <a href="#stats" class="text-gray-300 hover:text-white transition-colors">Data Analysis</a>
            <a href="#" class="text-gray-300 hover:text-white transition-colors">Blog</a>
        </div>
        <div class="flex items-center space-x-4">
            <button class="hidden md:flex bg-primary text-white px-6 py-2 rounded-button hover:bg-opacity-90 transition-all items-center hover-scale glow-effect">
                <i class="ri-chrome-fill mr-2"></i>
                Install Free Plugin
            </button>
            <button onclick="toggleMobileMenu()" class="md:hidden text-2xl text-gray-400 hover:text-white transition-colors">
                <i class="ri-menu-line"></i>
            </button>
        </div>
    </nav>

    <script>
    function toggleMobileMenu() {
        const menu = document.getElementById('mobileMenu');
        menu.classList.toggle('active');
        document.body.style.overflow = menu.classList.contains('active') ? 'hidden' : '';
    }

    // 导航栏滚动效果
    let lastScroll = 0;
    const nav = document.querySelector('nav');
    window.addEventListener('scroll', () => {
        const currentScroll = window.pageYOffset;
        if (currentScroll <= 0) {
            nav.style.transform = 'translateY(0)';
            return;
        }
        if (currentScroll > lastScroll && currentScroll > 80) {
            nav.style.transform = 'translateY(-100%)';
        } else {
            nav.style.transform = 'translateY(0)';
        }
        lastScroll = currentScroll;
    });
    </script>

    <section class="hero-bg min-h-screen flex items-center relative overflow-hidden">
        <!-- 背景动画元素 -->
        <div class="absolute inset-0 z-0">
            <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/20 rounded-full filter blur-3xl animate-pulse"></div>
            <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-secondary/20 rounded-full filter blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div class="container mx-auto px-6 md:px-12 py-32 relative z-10">
            <div class="max-w-5xl mx-auto">
                <!-- 主标题区域 -->
                <div class="text-center mb-16">
                    <h1 class="text-4xl sm:text-5xl md:text-7xl font-bold mb-8 leading-tight opacity-0 translate-y-8 transition-all duration-1000" id="heroTitle">
                        <span class="gradient-text">AI Data Analysis</span><br>
                        <span class="text-white">Redefining Trading Decisions</span>
                    </h1>
                    <p class="text-gray-300 text-lg sm:text-xl mb-12 max-w-2xl mx-auto opacity-0 translate-y-8 transition-all duration-1000 delay-300" id="heroDesc">
                        Integrating on-chain data, exchange data, and sentiment analysis to ensure every trade is based on data-driven insights
                    </p>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16 opacity-0 translate-y-8 transition-all duration-1000 delay-500" id="heroButtons">
                    <button class="hover-scale glow-effect bg-primary text-white px-8 py-4 rounded-button flex items-center whitespace-nowrap group">
                        <i class="ri-chrome-fill text-xl mr-2 group-hover:rotate-12 transition-transform"></i>
                        Add to Chrome
                    </button>
                    <a href="#features" class="text-gray-400 hover:text-white transition-colors flex items-center group">
                        Learn More
                        <i class="ri-arrow-right-line ml-2 group-hover:translate-x-2 transition-transform"></i>
                    </a>
                </div>

                <!-- Supported Exchanges -->
                <div class="opacity-0 translate-y-8 transition-all duration-1000 delay-700" id="heroExchanges">
                    <p class="text-center text-gray-400 text-sm mb-8">Supporting Major Exchanges</p>
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-8 justify-items-center max-w-4xl mx-auto">
                        <div class="glass-card px-4 py-3 rounded-lg flex items-center justify-center hover-scale cursor-pointer group">
                            <img src="{% static 'website/images/binance.ico' %}" alt="Binance" class="h-6 w-6 mr-2 group-hover:scale-110 transition-transform"/>
                            <span class="font-medium text-gray-300 group-hover:text-white transition-colors">Binance</span>
                        </div>
                        <div class="glass-card px-4 py-3 rounded-lg flex items-center justify-center hover-scale cursor-pointer group">
                            <img src="{% static 'website/images/okx.ico' %}" alt="OKX" class="h-6 w-6 mr-2 group-hover:scale-110 transition-transform"/>
                            <span class="font-medium text-gray-300 group-hover:text-white transition-colors">OKX</span>
                        </div>
                        <div class="glass-card px-4 py-3 rounded-lg flex items-center justify-center hover-scale cursor-pointer group">
                            <img src="{% static 'website/images/gate.ico' %}" alt="Gate.io" class="h-6 w-6 mr-2 group-hover:scale-110 transition-transform"/>
                            <span class="font-medium text-gray-300 group-hover:text-white transition-colors">Gate.io</span>
                        </div>
                        <div class="glass-card px-4 py-3 rounded-lg flex items-center justify-center hover-scale cursor-pointer group">
                            <img src="{% static 'website/images/bybit.ico' %}" alt="Bybit" class="h-6 w-6 mr-2 group-hover:scale-110 transition-transform"/>
                            <span class="font-medium text-gray-300 group-hover:text-white transition-colors">Bybit</span>
                        </div>
                        <div class="glass-card px-4 py-3 rounded-lg flex items-center justify-center hover-scale cursor-pointer group">
                            <img src="{% static 'website/images/htx.ico' %}" alt="Htx" class="h-6 w-6 mr-2 group-hover:scale-110 transition-transform"/>
                            <span class="font-medium text-gray-300 group-hover:text-white transition-colors">Htx</span>
                        </div>
                    </div>
                </div>

                <!-- Scroll Down Indicator -->
                <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-gray-400 scroll-indicator cursor-pointer">
                    <i class="ri-arrow-down-line text-2xl"></i>
                </div>
            </div>
        </div>
    </section>



    <script>
    // Hero 区域动画
    document.addEventListener('DOMContentLoaded', () => {
        const elements = ['heroTitle', 'heroDesc', 'heroButtons', 'heroExchanges'];
        elements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, 100);
            }
        });
    });
    </script>

    <section id="features" class="bg-dark-900 py-32 relative overflow-hidden">
        <!-- Background Decorations -->
        <div class="absolute inset-0 opacity-30">
            <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary to-transparent"></div>
            <div class="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary to-transparent"></div>
        </div>

        <div class="container mx-auto px-6 md:px-12">
            <!-- Title Section -->
            <div class="text-center mb-20" data-aos="fade-up">
                <h2 class="text-4xl md:text-5xl font-bold mb-6">
                    <span class="gradient-text">Professional</span> Trading Analysis Tools
                </h2>
                <p class="text-gray-400 max-w-2xl mx-auto text-lg">Deep learning-based market analysis system providing precise trading signals and risk assessment</p>
            </div>

            <!-- Market Analysis Display -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
                <!-- Left Side: Market Card -->
                <div class="glass-card rounded-xl p-8 space-y-6 hover-lift" data-aos="fade-right">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center space-x-3">
                            <img src="{% static 'website/images/btc.png' %}" alt="BTC" class="w-8 h-8">
                            <h3 class="text-2xl font-bold">BTC/USDT</h3>
                        </div>
                        <span class="text-gray-400 text-sm">Real-time Analysis</span>
                    </div>
                    
                    <div class="flex items-baseline">
                        <span class="text-4xl font-bold">96,588.02</span>
                        <span class="text-[#4CAF50] ml-3">+2.34%</span>
                    </div>

                    <div class="bg-dark-700 rounded-lg p-6">
                        <h4 class="text-lg font-semibold mb-4">Market Trend Analysis</h4>
                        <div class="grid grid-cols-3 gap-4">
                            <div class="bg-[#1B3B2D] text-[#4CAF50] rounded-lg p-4 text-center hover-scale">
                                <span class="block text-2xl font-bold mb-1">70%</span>
                                <span class="text-sm">Bullish</span>
                            </div>
                            <div class="bg-dark-800 text-gray-400 rounded-lg p-4 text-center hover-scale">
                                <span class="block text-2xl font-bold mb-1">20%</span>
                                <span class="text-sm">Neutral</span>
                            </div>
                            <div class="bg-[#3B1E1E] text-[#F44336] rounded-lg p-4 text-center hover-scale">
                                <span class="block text-2xl font-bold mb-1">10%</span>
                                <span class="text-sm">Bearish</span>
                            </div>
                        </div>
                    </div>

                    <div class="pt-4">
                        <div class="flex items-center justify-between text-sm text-gray-400">
                            <span>Volume (24h)</span>
                            <span>$12.5B</span>
                        </div>
                        <div class="flex items-center justify-between text-sm text-gray-400 mt-2">
                            <span>Open Interest</span>
                            <span>$8.2B</span>
                        </div>
                    </div>
                </div>

                <!-- Right Side: AI Analysis Features -->
                <div class="space-y-8" data-aos="fade-left">
                    <h3 class="text-2xl font-bold">AI-Driven Market Analysis</h3>
                    <ul class="space-y-8">
                        <li class="flex items-start transform hover:translate-x-2 transition-transform">
                            <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center shrink-0 mt-1 mr-4">
                                <i class="ri-line-chart-line text-primary text-xl"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold mb-2">Real-time Trend Prediction</h4>
                                <p class="text-gray-400">Analyzing market trend probabilities using deep learning models, providing clear directional guidance</p>
                            </div>
                        </li>
                        <li class="flex items-start transform hover:translate-x-2 transition-transform">
                            <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center shrink-0 mt-1 mr-4">
                                <i class="ri-stack-line text-primary text-xl"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold mb-2">Technical Indicator Integration</h4>
                                <p class="text-gray-400">Combining multiple key technical indicators for comprehensive market insights</p>
                            </div>
                        </li>
                        <li class="flex items-start transform hover:translate-x-2 transition-transform">
                            <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center shrink-0 mt-1 mr-4">
                                <i class="ri-shield-check-line text-primary text-xl"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold mb-2">Risk Assessment System</h4>
                                <p class="text-gray-400">Intelligent market risk assessment to safeguard your trading decisions</p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Feature Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="glass-card p-6 rounded-xl hover-lift" data-aos="fade-up" data-aos-delay="0">
                    <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                        <i class="ri-ai-generate text-primary text-3xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">AI Data Analysis</h3>
                    <p class="text-gray-400">Using advanced AI algorithms to analyze massive market data, identifying potential trading opportunities and risks</p>
                </div>

                <div class="glass-card p-6 rounded-xl hover-lift" data-aos="fade-up" data-aos-delay="100">
                    <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                        <i class="ri-line-chart-line text-primary text-3xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Real-time Market Monitoring</h3>
                    <p class="text-gray-400">24/7 continuous market monitoring, capturing price movements and market anomalies in real-time</p>
                </div>

                <div class="glass-card p-6 rounded-xl hover-lift" data-aos="fade-up" data-aos-delay="200">
                    <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                        <i class="ri-database-2-line text-primary text-3xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Multi-source Data Integration</h3>
                    <p class="text-gray-400">Integrating on-chain data, exchange data, and social media sentiment for a comprehensive market perspective</p>
                </div>

                <div class="glass-card p-6 rounded-xl hover-lift" data-aos="fade-up" data-aos-delay="300">
                    <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                        <i class="ri-notification-3-line text-primary text-3xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Smart Trading Alerts</h3>
                    <p class="text-gray-400">Automatically sending trading opportunity alerts based on your conditions, ensuring you never miss potential opportunities</p>
                </div>
            </div>
        </div>
    </section>



    <section id="how-it-works" class="bg-[#0F1012] py-32 relative overflow-hidden">
        <div class="container mx-auto px-6 md:px-12">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold mb-4">Three Steps to Smart Trading</h2>
                <p class="text-gray-400 max-w-2xl mx-auto text-lg">A simple and quick setup process that lets you immediately experience the advantages of AI data analysis.</p>
            </div>
            <div class="relative">
                <div class="absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-primary/20 via-primary to-primary/20 transform -translate-y-1/2"></div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 relative z-10">
                    <div class="step-card bg-[#1A1B1E] p-8 rounded-xl border border-gray-800/50">
                        <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6 relative">
                            <span class="text-3xl font-bold text-primary">1</span>
                            <div class="absolute -right-2 -top-2 w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                                <i class="ri-chrome-fill text-sm"></i>
                            </div>
                        </div>
                        <h3 class="text-xl font-semibold text-center mb-4">Install Extension</h3>
                        <p class="text-gray-400 text-center">One-click installation from the Chrome Web Store, no complex configuration needed</p>
                    </div>
                    <div class="step-card bg-[#1A1B1E] p-8 rounded-xl border border-gray-800/50">
                        <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6 relative">
                            <span class="text-3xl font-bold text-primary">2</span>
                            <div class="absolute -right-2 -top-2 w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                                <i class="ri-settings-3-fill text-sm"></i>
                            </div>
                        </div>
                        <h3 class="text-xl font-semibold text-center mb-4">Personalized Settings</h3>
                        <p class="text-gray-400 text-center">Customize analysis indicators based on your trading habits</p>
                    </div>
                    <div class="step-card bg-[#1A1B1E] p-8 rounded-xl border border-gray-800/50">
                        <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6 relative">
                            <span class="text-3xl font-bold text-primary">3</span>
                            <div class="absolute -right-2 -top-2 w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                                <i class="ri-rocket-2-fill text-sm"></i>
                            </div>
                        </div>
                        <h3 class="text-xl font-semibold text-center mb-4">Start Trading</h3>
                        <p class="text-gray-400 text-center">Enjoy the trading advantages brought by AI data analysis</p>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <section class="bg-[#15161A] py-32 relative overflow-hidden">
        <div class="container mx-auto px-6 md:px-12">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <div>
                    <h2 class="text-4xl font-bold mb-8">Comprehensive Data Integration</h2>
                    <p class="text-gray-400 text-lg mb-12">We integrate multiple reliable data sources to ensure we provide you with the most comprehensive and accurate market information.</p>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="data-source-card p-6 rounded-xl">
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                                <i class="ri-database-2-line text-xl text-primary"></i>
                            </div>
                            <h3 class="text-lg font-semibold mb-2">On-chain Data</h3>
                            <p class="text-gray-400">Real-time monitoring of blockchain transaction flows, address activity, and other key indicators</p>
                        </div>
                        <div class="data-source-card p-6 rounded-xl">
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                                <i class="ri-exchange-line text-xl text-primary"></i>
                            </div>
                            <h3 class="text-lg font-semibold mb-2">Exchange Data</h3>
                            <p class="text-gray-400">Integrating depth data and trading information from major global exchanges</p>
                        </div>
                        <div class="data-source-card p-6 rounded-xl">
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                                <i class="ri-chat-poll-line text-xl text-primary"></i>
                            </div>
                            <h3 class="text-lg font-semibold mb-2">Sentiment Analysis</h3>
                            <p class="text-gray-400">Analyzing market sentiment indicators from social media and news outlets</p>
                        </div>
                        <div class="data-source-card p-6 rounded-xl">
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                                <i class="ri-funds-line text-xl text-primary"></i>
                            </div>
                            <h3 class="text-lg font-semibold mb-2">Capital Flow</h3>
                            <p class="text-gray-400">Tracking institutional investors' fund movements and position changes</p>
                        </div>
                    </div>
                </div>
                <div class="relative">
                    <img src="https://readdy.ai/api/search-image?query=A%20modern%20data%20visualization%20showing%20multiple%20data%20streams%20converging%20into%20a%20central%20AI%20analytics%20hub.%20The%20scene%20features%20holographic%20displays%20with%20flowing%20data%2C%20market%20charts%2C%20and%20network%20connections.%20Professional%203D%20render%20with%20blue%20accent%20lighting%20and%20depth%20of%20field.&width=800&height=600&seq=789&orientation=landscape" alt="数据整合" class="rounded-xl w-full object-cover shadow-2xl">
                    <div class="absolute inset-0 bg-gradient-to-r from-[#15161A] via-transparent to-transparent"></div>
                </div>
            </div>
        </div>
    </section>



    <section class="bg-[#0F1012] py-32">
        <div class="container mx-auto px-6 md:px-12">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold mb-4">What Users Say</h2>
                <p class="text-gray-400 max-w-2xl mx-auto text-lg">Reviews from real users, learn how they use our tools to improve trading efficiency.</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-[#1A1B1E] p-8 rounded-xl border border-gray-800/50">
                    <div class="flex items-center mb-4">
                        <div class="w-5 h-5 flex items-center justify-center text-yellow-400">
                            <i class="ri-star-fill"></i>
                        </div>
                        <div class="w-5 h-5 flex items-center justify-center text-yellow-400">
                            <i class="ri-star-fill"></i>
                        </div>
                        <div class="w-5 h-5 flex items-center justify-center text-yellow-400">
                            <i class="ri-star-fill"></i>
                        </div>
                        <div class="w-5 h-5 flex items-center justify-center text-yellow-400">
                            <i class="ri-star-fill"></i>
                        </div>
                        <div class="w-5 h-5 flex items-center justify-center text-yellow-400">
                            <i class="ri-star-fill"></i>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-6">"This extension has completely changed my trading approach. AI data analysis has helped me identify many trading opportunities I would have missed before, significantly improving my investment returns."</p>
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-primary/20 rounded-full flex items-center justify-center mr-3">
                            <span class="text-primary font-semibold">LZ</span>
                        </div>
                        <div>
                            <h4 class="font-semibold">Liu Zhiqiang</h4>
                            <p class="text-gray-500 text-sm">Professional Trader · 3 Years Experience</p>
                        </div>
                    </div>
                </div>
                <div class="bg-[#1A1B1E] p-8 rounded-xl border border-gray-800/50">
                    <div class="flex items-center mb-4">
                        <div class="w-5 h-5 flex items-center justify-center text-yellow-400">
                            <i class="ri-star-fill"></i>
                        </div>
                        <div class="w-5 h-5 flex items-center justify-center text-yellow-400">
                            <i class="ri-star-fill"></i>
                        </div>
                        <div class="w-5 h-5 flex items-center justify-center text-yellow-400">
                            <i class="ri-star-fill"></i>
                        </div>
                        <div class="w-5 h-5 flex items-center justify-center text-yellow-400">
                            <i class="ri-star-fill"></i>
                        </div>
                        <div class="w-5 h-5 flex items-center justify-center text-yellow-400">
                            <i class="ri-star-fill"></i>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-6">"As a part-time trader, I don't have much time to research the market. This extension's real-time monitoring and smart alert features allow me to not miss important trading opportunities even while working."</p>
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-primary/20 rounded-full flex items-center justify-center mr-3">
                            <span class="text-primary font-semibold">WY</span>
                        </div>
                        <div>
                            <h4 class="font-semibold">Wang Ying</h4>
                            <p class="text-gray-500 text-sm">Part-time Trader · 1.5 Years Experience</p>
                        </div>
                    </div>
                </div>
                <div class="bg-[#1A1B1E] p-8 rounded-xl border border-gray-800/50">
                    <div class="flex items-center mb-4">
                        <div class="w-5 h-5 flex items-center justify-center text-yellow-400">
                            <i class="ri-star-fill"></i>
                        </div>
                        <div class="w-5 h-5 flex items-center justify-center text-yellow-400">
                            <i class="ri-star-fill"></i>
                        </div>
                        <div class="w-5 h-5 flex items-center justify-center text-yellow-400">
                            <i class="ri-star-fill"></i>
                        </div>
                        <div class="w-5 h-5 flex items-center justify-center text-yellow-400">
                            <i class="ri-star-fill"></i>
                        </div>
                        <div class="w-5 h-5 flex items-center justify-center text-yellow-400">
                            <i class="ri-star-half-fill"></i>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-6">"Multi-source data integration is this extension's biggest highlight. Being able to see on-chain data, exchange data, and sentiment analysis simultaneously gives me a more comprehensive understanding of the market, making trading decisions more rational."</p>
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-primary/20 rounded-full flex items-center justify-center mr-3">
                            <span class="text-primary font-semibold">ZM</span>
                        </div>
                        <div>
                            <h4 class="font-semibold">Zhao Minghui</h4>
                            <p class="text-gray-500 text-sm">Institutional Investor · 5 Years Experience</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 联系我们 -->
     <section class="bg-dark-900 py-32 relative overflow-hidden">
         <div class="absolute inset-0 opacity-30">
             <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary to-transparent"></div>
             <div class="absolute -bottom-10 -left-10 w-72 h-72 bg-primary/30 rounded-full filter blur-3xl"></div>
             <div class="absolute -top-10 -right-10 w-72 h-72 bg-primary/30 rounded-full filter blur-3xl"></div>
         </div>
 
         <div class="container mx-auto px-6 md:px-12">
             <div class="max-w-4xl mx-auto text-center" data-aos="fade-up">
                 <h2 class="text-4xl md:text-5xl font-bold mb-6">
                     <span class="gradient-text">Contact Us</span>
                 </h2>
                 <p class="text-gray-400 text-lg mb-12">Subscribe to our updates for the latest market analysis and trading signals</p>
 
                 <form class="max-w-md mx-auto">
                     <div class="flex flex-col sm:flex-row gap-4">
                         <input type="email" placeholder="Enter your email address" class="flex-1 px-6 py-4 bg-dark-800 rounded-xl border border-gray-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-all">
                         <button type="submit" class="btn-primary px-8 py-4 rounded-xl hover:scale-105 transition-transform">
                             Subscribe
                         </button>
                     </div>
                 </form>
 
                 <div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
                     <div class="glass-card p-6 rounded-xl hover-lift text-center">
                         <div class="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                             <i class="ri-mail-line text-primary text-2xl"></i>
                         </div>
                         <h3 class="font-semibold mb-2">Email Support</h3>
                         <p class="text-gray-400"><EMAIL></p>
                     </div>
 
                     <div class="glass-card p-6 rounded-xl hover-lift text-center">
                         <div class="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                             <i class="ri-telegram-line text-primary text-2xl"></i>
                         </div>
                         <h3 class="font-semibold mb-2">Telegram Group</h3>
                         <p class="text-gray-400">@cooltrade_official</p>
                     </div>
 
                     <div class="glass-card p-6 rounded-xl hover-lift text-center">
                         <div class="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                             <i class="ri-twitter-x-line text-primary text-2xl"></i>
                         </div>
                         <h3 class="font-semibold mb-2">Twitter</h3>
                         <p class="text-gray-400">@cooltrade_ai</p>
                     </div>
                 </div>
             </div>
         </div>
     </section>

     <!-- 页脚 -->
     <footer class="bg-[#0F1012] py-16">
         <div class="container mx-auto px-6 md:px-12">
             <div class="grid grid-cols-1 md:grid-cols-4 gap-12 mb-12">
                 <div class="space-y-4">
                     <img src="https://cooltrade.xyz/static/images/logo.png" alt="CoolTrade" class="h-8">
                     <p class="text-gray-400">Professional Cryptocurrency Trading Analysis Tool</p>
                     <div class="flex space-x-4">
                         <a href="#" class="text-gray-400 hover:text-primary transition-colors">
                             <i class="ri-telegram-fill text-xl"></i>
                         </a>
                         <a href="#" class="text-gray-400 hover:text-primary transition-colors">
                             <i class="ri-twitter-x-fill text-xl"></i>
                         </a>
                         <a href="#" class="text-gray-400 hover:text-primary transition-colors">
                             <i class="ri-github-fill text-xl"></i>
                         </a>
                     </div>
                 </div>

                 <div>
                     <h4 class="font-semibold mb-4">Product</h4>
                     <ul class="space-y-2">
                         <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Features</a></li>
                         <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Tutorials</a></li>
                         <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Pricing</a></li>
                         <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Changelog</a></li>
                     </ul>
                 </div>

                 <div>
                     <h4 class="font-semibold mb-4">Support</h4>
                     <ul class="space-y-2">
                         <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Help Center</a></li>
                         <li><a href="#" class="text-gray-400 hover:text-white transition-colors">API Documentation</a></li>
                         <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Community</a></li>
                         <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Contact Us</a></li>
                     </ul>
                 </div>

                 <div>
                     <h4 class="font-semibold mb-4">Legal</h4>
                     <ul class="space-y-2">
                         <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Terms of Service</a></li>
                         <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a></li>
                         <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Disclaimer</a></li>
                     </ul>
                 </div>
             </div>

             <div class="border-t border-gray-800 pt-8">
                 <div class="flex flex-col md:flex-row justify-between items-center">
                     <p class="text-gray-400 text-sm">© 2024 CoolTrade. All rights reserved.</p>
                     <div class="flex space-x-6 mt-4 md:mt-0">
                         <a href="#" class="text-gray-400 hover:text-white transition-colors text-sm">Privacy Policy</a>
                         <a href="#" class="text-gray-400 hover:text-white transition-colors text-sm">Terms of Service</a>
                         <a href="#" class="text-gray-400 hover:text-white transition-colors text-sm">Cookie Policy</a>
                     </div>
                 </div>
             </div>
         </div>
     </footer>
</body>
</html>