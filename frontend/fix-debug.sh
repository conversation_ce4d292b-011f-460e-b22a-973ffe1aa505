#!/bin/bash

# 批量替换调试条件的脚本

echo "开始修复调试条件..."

# 替换所有文件中的调试条件
find src -name "*.ts" -o -name "*.vue" | xargs sed -i '' 's/process\.env\.NODE_ENV === '\''development'\'' || localStorage\.getItem('\''i18n_debug'\'') === '\''true'\''/process.env.NODE_ENV === '\''development'\''/g'

echo "修复完成！"

# 显示修改的文件
echo "已修改的文件："
find src -name "*.ts" -o -name "*.vue" | xargs grep -l "process\.env\.NODE_ENV === 'development'" | head -10
