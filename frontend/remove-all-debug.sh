#!/bin/bash

echo "🧹 开始移除所有调试代码..."

# 移除所有 console.log 语句
echo "移除 console.log 语句..."
find src -name "*.ts" -o -name "*.vue" | xargs sed -i '' '/console\.log/d'

# 移除所有 I18n Debug 相关的日志
echo "移除 I18n Debug 日志..."
find src -name "*.ts" -o -name "*.vue" | xargs sed -i '' '/\[I18n Debug\]/d'

# 移除 === I18N DEBUG === 块
echo "移除 I18N DEBUG 块..."
find src -name "*.ts" -o -name "*.vue" | xargs sed -i '' '/=== I18N DEBUG START ===/,/=== I18N DEBUG END ===/d'

# 移除 console.error 语句
echo "移除 console.error 语句..."
find src -name "*.ts" -o -name "*.vue" | xargs sed -i '' '/console\.error/d'

# 移除 console.warn 语句
echo "移除 console.warn 语句..."
find src -name "*.ts" -o -name "*.vue" | xargs sed -i '' '/console\.warn/d'

echo "✅ 调试代码移除完成！"

# 显示剩余的调试代码
echo "检查剩余的调试代码："
find src -name "*.ts" -o -name "*.vue" | xargs grep -n "console\." | head -5 || echo "✨ 没有发现剩余的调试代码！"
