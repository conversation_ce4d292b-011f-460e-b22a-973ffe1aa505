// 清除调试标志的脚本
// 在浏览器控制台中运行此脚本来清除 i18n 调试日志

console.log('清除所有调试标志...');

// 移除调试标志
localStorage.removeItem('i18n_debug');

// 清除调试对象
if (window.i18nDebug) {
  delete window.i18nDebug;
}

// 清除其他可能的调试标志
localStorage.removeItem('debug');
localStorage.removeItem('app_debug');

console.log('所有调试标志已清除，请刷新页面以生效');

// 自动刷新页面
setTimeout(() => {
  window.location.reload();
}, 1000);

// 也可以手动运行以下命令来启用调试：
// localStorage.setItem('i18n_debug', 'true'); window.location.reload();
