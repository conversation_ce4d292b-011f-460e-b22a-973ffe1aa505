import './styles/main.css'
import { createApp } from 'vue'
import App from './App.vue'
import router from './router/index'
import { createPinia } from 'pinia'
import 'remixicon/fonts/remixicon.css'
import 'element-plus/dist/index.css'
import i18n from './i18n' // 导入i18n配置
import directI18n, { i18nDirectPlugin } from './i18n/direct-loader' // 导入直接加载器作为备用方案

// 移除所有调试代码

// 移除 i18n 调试工具

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(i18n) // 使用i18n
app.use(i18nDirectPlugin) // 使用直接加载器作为备用方案

// 全局属性，方便调试
app.config.globalProperties.$i18n = i18n
app.config.globalProperties.$t = i18n.global.t

// 移除 i18n 测试代码

app.mount('#app')
