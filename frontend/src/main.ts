import './styles/main.css'
import { createApp } from 'vue'
import App from './App.vue'
import router from './router/index'
import { createPinia } from 'pinia'
import 'remixicon/fonts/remixicon.css'
import 'element-plus/dist/index.css'
import i18n from './i18n' // 导入i18n配置
import directI18n, { i18nDirectPlugin } from './i18n/direct-loader' // 导入直接加载器作为备用方案

// 详细调试 i18n 加载情况
console.log('=== I18N DEBUG START ===')
console.log('Available languages:', Object.keys(i18n.global.messages.value))

// 检查每种语言的消息是否正确加载
const languages = Object.keys(i18n.global.messages.value)
languages.forEach(lang => {
  const messages = i18n.global.messages.value[lang]
  console.log(`Language ${lang} loaded:`, !!messages)

  // 检查关键消息是否存在
  if (messages) {
    console.log(`Sample translations for ${lang}:`)
    console.log('- auth.login:', messages.auth?.login || 'NOT FOUND')
    console.log('- auth.email:', messages.auth?.email || 'NOT FOUND')
    console.log('- common.loading:', messages.common?.loading || 'NOT FOUND')
  }
})

// 检查当前语言
console.log('Current locale:', i18n.global.locale.value)

// 导入 i18n 调试工具
import { initI18nDebug } from './i18n-debug';

// 初始化 i18n 调试
const i18nDebug = initI18nDebug();

// 打印所有可用的语言
console.log('Available locales:', Object.keys(i18n.global.messages.value));
console.log('=== I18N DEBUG END ===')

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(i18n) // 使用i18n
app.use(i18nDirectPlugin) // 使用直接加载器作为备用方案

// 全局属性，方便调试
app.config.globalProperties.$i18n = i18n
app.config.globalProperties.$t = i18n.global.t

// 只在开发环境中进行 i18n 测试
if (process.env.NODE_ENV === 'development') {
  try {
    const testTranslation = i18n.global.t('auth.login')
    const isWorking = testTranslation !== 'auth.login'
    console.log('I18n working properly:', isWorking)

    // 检测是否在扩展环境中
    const isExtension = window.location.protocol.includes('extension') ||
                        window.location.protocol.includes('chrome') ||
                        window.location.protocol.includes('moz')

    console.log('Running in extension environment:', isExtension)
  } catch (e) {
    console.error('Error testing i18n:', e)
  }
}

app.mount('#app')
