<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cooltrade</title>
    <style>
      html, body {
        width: 375px;
        height: 600px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        background-color: #0F172A;
      }
      #app {
        width: 100%;
        height: 100%;
        overflow: hidden;
      }
    </style>
    <!-- 内联脚本已移动到 main.ts 中 -->
    <link rel="stylesheet" crossorigin href="./assets/main.css">
    <script type="module" crossorigin src="./assets/main.js"></script>
    <link rel="stylesheet" crossorigin href="./assets/main.css">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
